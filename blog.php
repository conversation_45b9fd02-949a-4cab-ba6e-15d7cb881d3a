<?php 
require_once 'config.php';

// Blog categories
$blog_categories = [
    'all' => 'All Posts',
    'industry-insights' => 'Industry Insights',
    'shipping-guides' => 'Shipping Guides',
    'market-trends' => 'Market Trends',
    'technology' => 'Technology',
    'sustainability' => 'Sustainability',
    'regulations' => 'Regulations'
];

// Sample blog posts data
$blog_posts = [
    [
        'id' => 1,
        'title' => 'The Future of Global Supply Chain: Trends to Watch in 2024',
        'excerpt' => 'Explore the emerging trends shaping the global supply chain landscape, from AI-driven logistics to sustainable shipping practices.',
        'category' => 'industry-insights',
        'author' => 'PROMTECH Team',
        'date' => '2024-01-15',
        'image' => 'img8.webp',
        'featured' => true,
        'tags' => ['Supply Chain', 'Technology', 'Trends'],
        'read_time' => '8 min read'
    ],
    [
        'id' => 2,
        'title' => 'Complete Guide to Ocean Freight: FCL vs LCL Shipping',
        'excerpt' => 'Understanding the differences between Full Container Load and Less than Container Load shipping to optimise your logistics costs.',
        'category' => 'shipping-guides',
        'author' => 'Maritime Expert',
        'date' => '2024-01-10',
        'image' => 'img9.webp',
        'featured' => false,
        'tags' => ['Ocean Freight', 'FCL', 'LCL'],
        'read_time' => '12 min read'
    ],
    [
        'id' => 3,
        'title' => 'Grain Market Analysis: Ukraine Export Opportunities',
        'excerpt' => 'In-depth analysis of Ukrainian grain export markets and emerging opportunities in global agricultural trade.',
        'category' => 'market-trends',
        'author' => 'Agricultural Analyst',
        'date' => '2024-01-08',
        'image' => 'grain.jpg',
        'featured' => true,
        'tags' => ['Grain', 'Ukraine', 'Export'],
        'read_time' => '10 min read'
    ],
    [
        'id' => 4,
        'title' => 'Digital Transformation in Logistics: IoT and Real-Time Tracking',
        'excerpt' => 'How Internet of Things technology is revolutionising cargo tracking and supply chain visibility.',
        'category' => 'technology',
        'author' => 'Tech Specialist',
        'date' => '2024-01-05',
        'image' => 'img10.webp',
        'featured' => false,
        'tags' => ['IoT', 'Tracking', 'Digital'],
        'read_time' => '6 min read'
    ],
    [
        'id' => 5,
        'title' => 'Sustainable Shipping: Green Logistics Solutions for 2024',
        'excerpt' => 'Exploring eco-friendly shipping options and sustainable practices that reduce environmental impact while maintaining efficiency.',
        'category' => 'sustainability',
        'author' => 'Sustainability Expert',
        'date' => '2024-01-03',
        'image' => 'img11.webp',
        'featured' => false,
        'tags' => ['Sustainability', 'Green Logistics', 'Environment'],
        'read_time' => '9 min read'
    ],
    [
        'id' => 6,
        'title' => 'New EU Customs Regulations: What Importers Need to Know',
        'excerpt' => 'Comprehensive overview of the latest European Union customs regulations and their impact on international trade.',
        'category' => 'regulations',
        'author' => 'Customs Expert',
        'date' => '2024-01-01',
        'image' => 'img12.webp',
        'featured' => false,
        'tags' => ['EU', 'Customs', 'Regulations'],
        'read_time' => '7 min read'
    ]
];

// Get current category filter
$current_category = $_GET['category'] ?? 'all';

// Filter posts by category
$filtered_posts = $current_category === 'all' 
    ? $blog_posts 
    : array_filter($blog_posts, function($post) use ($current_category) {
        return $post['category'] === $current_category;
    });

// Get featured posts
$featured_posts = array_filter($blog_posts, function($post) {
    return $post['featured'];
});
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logistics Blog - Industry Insights | <?php echo COMPANY_NAME; ?></title>
    <meta name="description" content="Stay updated with the latest logistics industry insights, shipping guides, market trends, and expert analysis from PROMTECH's logistics professionals.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colours: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .hero-banner {
            background: linear-gradient(rgba(30, 58, 138, 0.8), rgba(255, 122, 61, 0.6)), url('<?php echo getAssetUrl('assets/img/img13.webp'); ?>');
            background-size: cover;
            background-position: centre centre;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        .blog-card {
            transition: all 0.3s ease;
        }
        .blog-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .category-filter {
            transition: all 0.3s ease;
        }
        .category-filter.active {
            background: linear-gradient(135deg, #FF7A3D, #1E3A8A);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>

    <!-- Hero Banner Section -->
    <section class="hero-banner min-h-[500px] flex items-center justify-center relative">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary/90 to-primary/70"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <nav class="flex items-center justify-center space-x-2 mb-6">
                    <a href="index.php" class="text-white/80 hover:text-white transition-colours">
                        <i class="ri-home-line mr-1"></i>Home
                    </a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <span class="text-white font-medium">Blog</span>
                </nav>
                <h1 class="text-5xl md:text-6xl font-bold mb-6">Logistics Insights</h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    Stay ahead of the curve with expert insights, industry trends, and practical guides from our logistics professionals.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="#latest-posts" class="bg-white text-secondary px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colours flex items-center justify-center">
                        <i class="ri-article-line mr-2"></i>
                        Read Latest Posts
                    </a>
                    <a href="#categories" class="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/20 transition-colours flex items-center justify-center">
                        <i class="ri-bookmark-line mr-2"></i>
                        Browse Categories
                    </a>
                </div>
            </div>
        </div>
    </section>

    <!-- Featured Posts -->
    <?php if (!empty($featured_posts)): ?>
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <span class="text-primary font-semibold text-lg">Featured Content</span>
                <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">Editor's Picks</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Our most popular and insightful articles covering the latest trends and developments in the logistics industry.
                </p>
            </div>

            <div class="grid lg:grid-cols-2 gap-12">
                <?php foreach ($featured_posts as $post): ?>
                <article class="blog-card bg-gradient-to-br from-gray-50 to-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="<?php echo getAssetUrl('assets/img/' . $post['image']); ?>" 
                             alt="<?php echo htmlspecialchars($post['title']); ?>"
                             class="w-full h-64 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                <?php echo $blog_categories[$post['category']]; ?>
                            </span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full text-sm">
                                <?php echo $post['read_time']; ?>
                            </span>
                        </div>
                    </div>
                    <div class="p-8">
                        <div class="flex items-center text-sm text-gray-500 mb-4">
                            <span><?php echo $post['author']; ?></span>
                            <span class="mx-2">•</span>
                            <span><?php echo date('M j, Y', strtotime($post['date'])); ?></span>
                        </div>
                        <h3 class="text-2xl font-bold text-secondary mb-4 hover:text-primary transition-colours">
                            <a href="blog-post.php?id=<?php echo $post['id']; ?>">
                                <?php echo htmlspecialchars($post['title']); ?>
                            </a>
                        </h3>
                        <p class="text-gray-600 mb-6 leading-relaxed">
                            <?php echo htmlspecialchars($post['excerpt']); ?>
                        </p>
                        <div class="flex flex-wrap gap-2 mb-6">
                            <?php foreach ($post['tags'] as $tag): ?>
                            <span class="bg-gray-100 text-gray-600 px-3 py-1 rounded-full text-sm">
                                <?php echo htmlspecialchars($tag); ?>
                            </span>
                            <?php endforeach; ?>
                        </div>
                        <a href="blog-post.php?id=<?php echo $post['id']; ?>" 
                           class="inline-flex items-center text-primary font-semibold hover:text-primary/80 transition-colours">
                            Read Full Article
                            <i class="ri-arrow-right-line ml-2"></i>
                        </a>
                    </div>
                </article>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- All Blog Posts -->
    <section id="latest-posts" class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-secondary mb-4">
                    <?php echo $current_category === 'all' ? 'Latest Articles' : $blog_categories[$current_category]; ?>
                </h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    <?php echo count($filtered_posts); ?> article<?php echo count($filtered_posts) !== 1 ? 's' : ''; ?> found
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <?php foreach ($filtered_posts as $post): ?>
                <?php if (!$post['featured']): // Don't show featured posts again ?>
                <article class="blog-card bg-white rounded-2xl shadow-lg overflow-hidden">
                    <div class="relative">
                        <img src="<?php echo getAssetUrl('assets/img/' . $post['image']); ?>"
                             alt="<?php echo htmlspecialchars($post['title']); ?>"
                             class="w-full h-48 object-cover">
                        <div class="absolute top-4 left-4">
                            <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                <?php echo $blog_categories[$post['category']]; ?>
                            </span>
                        </div>
                        <div class="absolute top-4 right-4">
                            <span class="bg-white/90 backdrop-blur-sm text-gray-700 px-3 py-1 rounded-full text-sm">
                                <?php echo $post['read_time']; ?>
                            </span>
                        </div>
                    </div>
                    <div class="p-6">
                        <div class="flex items-center text-sm text-gray-500 mb-3">
                            <span><?php echo $post['author']; ?></span>
                            <span class="mx-2">•</span>
                            <span><?php echo date('M j, Y', strtotime($post['date'])); ?></span>
                        </div>
                        <h3 class="text-xl font-bold text-secondary mb-3 hover:text-primary transition-colours">
                            <a href="blog-post.php?id=<?php echo $post['id']; ?>">
                                <?php echo htmlspecialchars($post['title']); ?>
                            </a>
                        </h3>
                        <p class="text-gray-600 mb-4 leading-relaxed">
                            <?php echo htmlspecialchars($post['excerpt']); ?>
                        </p>
                        <div class="flex flex-wrap gap-2 mb-4">
                            <?php foreach ($post['tags'] as $tag): ?>
                            <span class="bg-gray-100 text-gray-600 px-2 py-1 rounded text-xs">
                                <?php echo htmlspecialchars($tag); ?>
                            </span>
                            <?php endforeach; ?>
                        </div>
                        <a href="blog-post.php?id=<?php echo $post['id']; ?>"
                           class="inline-flex items-center text-primary font-semibold hover:text-primary/80 transition-colours">
                            Read More
                            <i class="ri-arrow-right-line ml-2"></i>
                        </a>
                    </div>
                </article>
                <?php endif; ?>
                <?php endforeach; ?>
            </div>

            <!-- Pagination (placeholder for future implementation) -->
            <div class="flex justify-center mt-12">
                <div class="flex space-x-2">
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colours">
                        <i class="ri-arrow-left-line"></i>
                    </button>
                    <button class="px-4 py-2 bg-primary text-white rounded-lg">1</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colours">2</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colours">3</button>
                    <button class="px-4 py-2 bg-gray-200 text-gray-600 rounded-lg hover:bg-gray-300 transition-colours">
                        <i class="ri-arrow-right-line"></i>
                    </button>
                </div>
            </div>
        </div>
    </section>

    <!-- Newsletter Subscription -->
    <section class="py-16 bg-gradient-to-r from-secondary to-primary">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <h2 class="text-3xl font-bold mb-4">Stay Updated with Industry Insights</h2>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-8">
                    Subscribe to our newsletter and never miss the latest logistics trends, shipping guides, and market analysis.
                </p>
                <form class="max-w-md mx-auto flex gap-4">
                    <input type="email" placeholder="Enter your email address"
                           class="flex-1 px-6 py-4 rounded-full text-gray-900 focus:outline-none focus:ring-2 focus:ring-white">
                    <button type="submit"
                            class="bg-white text-secondary px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colours flex items-center">
                        <i class="ri-mail-send-line mr-2"></i>
                        Subscribe
                    </button>
                </form>
                <p class="text-white/70 text-sm mt-4">
                    Join 5,000+ logistics professionals who trust our insights.
                </p>
            </div>
        </div>
    </section>

    <script>
        // Smooth scroll animation for blog cards
        document.addEventListener('DOMContentLoaded', function() {
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            // Observe all blog cards
            document.querySelectorAll('.blog-card').forEach(card => {
                card.style.opacity = '0';
                card.style.transform = 'translateY(20px)';
                card.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(card);
            });

            // Smooth scroll for anchor links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
    <?php endif; ?>

    <!-- Category Filter -->
    <section id="categories" class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-3xl font-bold text-secondary mb-4">Browse by Category</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Find articles that match your interests and expertise level.
                </p>
            </div>

            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <?php foreach ($blog_categories as $key => $name): ?>
                <a href="?category=<?php echo $key; ?>" 
                   class="category-filter px-6 py-3 rounded-full font-medium transition-colours <?php echo $current_category === $key ? 'active' : 'bg-white text-gray-700 hover:bg-gray-100'; ?>">
                    <?php echo $name; ?>
                </a>
                <?php endforeach; ?>
            </div>
        </div>
    </section>
