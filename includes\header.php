<?php
// Include configuration
require_once dirname(__DIR__) . '/config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo getPageTitle(); ?></title>
    <meta name="description" content="<?php echo getPageDescription(); ?>">
    <meta name="keywords" content="import, export, logistics, Ukraine, global trade, commodities, grain, wood, shipping">
    <meta name="author" content="PROMTECH EXPORT & IMPORT">
    
    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="<?php echo getPageTitle(); ?>">
    <meta property="og:description" content="<?php echo getPageDescription(); ?>">
    <meta property="og:type" content="website">
    <meta property="og:url" content="<?php echo SITE_URL; ?>">
    <meta property="og:site_name" content="<?php echo SITE_NAME; ?>">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo getAssetUrl('assets/favicon.ico'); ?>">
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Remix Icons -->
    <link href="https://cdn.jsdelivr.net/npm/remixicon@4.0.0/fonts/remixicon.css" rel="stylesheet">
    
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Pacifico&display=swap" rel="stylesheet">
    
    <!-- Custom Tailwind Configuration -->
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        primary: '#FF7A3D',
                        secondary: '#1E1E1E',
                        'primary-dark': '#E6692F',
                        'gray-50': '#F9FAFB',
                        'gray-100': '#F3F4F6',
                        'gray-600': '#4B5563',
                        'gray-800': '#1F2937',
                        'gray-900': '#111827'
                    },
                    fontFamily: {
                        'pacifico': ['Pacifico', 'cursive']
                    },
                    borderRadius: {
                        'button': '8px'
                    }
                }
            }
        }
    </script>
    
    <!-- Custom Styles -->
    <style>
        /* Desktop Dropdown Styles */
        .dropdown {
            position: relative;
        }

        .dropdown-menu {
            display: none;
            position: absolute;
            top: 100%;
            left: 0;
            background: white;
            min-width: 220px;
            border-radius: 12px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
            z-index: 1000;
            margin-top: 4px;
            opacity: 0;
            transform: translateY(-10px);
            transition: all 0.3s ease;
            border: 1px solid #e5e7eb;
            padding: 8px 0;
        }

        /* Create seamless hover area */
        .dropdown::before {
            content: '';
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            height: 12px;
            background: transparent;
            z-index: 999;
        }

        .dropdown:hover .dropdown-menu {
            display: block;
            opacity: 1;
            transform: translateY(0);
        }

        .dropdown-menu a {
            display: block;
            padding: 12px 20px;
            color: #374151;
            text-decoration: none;
            transition: all 0.2s ease;
            border-radius: 8px;
            margin: 2px 8px;
            position: relative;
            font-weight: 500;
        }

        .dropdown-menu a:hover {
            background-color: #f3f4f6;
            color: #FF7A3D;
            transform: translateX(4px);
        }

        .dropdown-menu a::before {
            content: '';
            position: absolute;
            left: 8px;
            top: 50%;
            transform: translateY(-50%);
            width: 4px;
            height: 4px;
            background-color: #FF7A3D;
            border-radius: 50%;
            opacity: 0;
            transition: opacity 0.2s ease;
        }

        .dropdown-menu a:hover::before {
            opacity: 1;
        }

        /* Improve dropdown button hover */
        .dropdown > button {
            padding: 10px 16px;
            border-radius: 8px;
            transition: all 0.2s ease;
            background: transparent;
            border: none;
            color: white;
            cursor: pointer;
        }

        .dropdown:hover > button {
            background-color: rgba(255, 122, 61, 0.15);
            color: #FF7A3D;
        }

        .dropdown > button i {
            transition: transform 0.2s ease;
        }

        .dropdown:hover > button i {
            transform: rotate(180deg);
        }

        /* Mobile Menu Styles */
        .mobile-menu-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            z-index: 998;
            display: none;
        }

        .mobile-menu-overlay.active {
            display: block;
        }

        .mobile-menu {
            position: fixed;
            top: 0;
            right: -100%;
            width: 280px;
            height: 100vh;
            background: white;
            z-index: 999;
            transition: right 0.3s ease;
            overflow-y: auto;
            box-shadow: -5px 0 15px rgba(0,0,0,0.1);
        }

        .mobile-menu.active {
            right: 0;
        }

        .mobile-menu-header {
            padding: 20px;
            border-bottom: 1px solid #e5e7eb;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .mobile-menu-close {
            background: none;
            border: none;
            font-size: 24px;
            color: #6b7280;
            cursor: pointer;
            padding: 8px;
            margin-left: auto;
        }

        .mobile-menu-nav {
            padding: 20px 0;
        }

        .mobile-menu-item {
            border-bottom: 1px solid #f3f4f6;
        }

        .mobile-menu-link {
            display: block;
            padding: 16px 20px;
            color: #374151;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.2s;
        }

        .mobile-menu-link:hover {
            background-color: #f9fafb;
            color: #FF7A3D;
        }

        .mobile-dropdown-btn {
            width: 100%;
            padding: 16px 20px;
            background: none;
            border: none;
            text-align: left;
            color: #374151;
            font-weight: 500;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.2s;
        }

        .mobile-dropdown-btn:hover {
            background-color: #f9fafb;
            color: #FF7A3D;
        }

        .mobile-submenu {
            background-color: #f8fafc;
            display: none;
            border-top: 1px solid #e2e8f0;
            animation: slideDown 0.3s ease;
        }

        .mobile-submenu.active {
            display: block;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                max-height: 0;
            }
            to {
                opacity: 1;
                max-height: 200px;
            }
        }

        .mobile-submenu a {
            display: block;
            padding: 14px 40px;
            color: #64748b;
            text-decoration: none;
            transition: all 0.2s;
            border-bottom: 1px solid #e2e8f0;
            position: relative;
        }

        .mobile-submenu a:last-child {
            border-bottom: none;
        }

        .mobile-submenu a::before {
            content: '→';
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            color: #FF7A3D;
            opacity: 0;
            transition: all 0.2s;
        }

        .mobile-submenu a:hover {
            color: #FF7A3D;
            background-color: #fff;
            padding-left: 50px;
        }

        .mobile-submenu a:hover::before {
            opacity: 1;
        }
        
        .hero-bg {
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.85) 0%, rgba(255, 122, 61, 0.15) 100%),
                        url('https://readdy.ai/api/search-image?query=PROMTECH%20export%20import%20logistics%20shipping%20containers%20port%20cranes%20cargo%20ships%20global%20trade%20operations%20Ukraine%20Kiev%20professional%20industrial%20background%20orange%20accent%2C%20high%20quality%2C%20wide%20angle%2C%20cinematic&width=1920&height=1080&seq=promtech-hero001&orientation=landscape');
            background-size: cover;
            background-position: center;
            background-attachment: fixed;
            position: relative;
        }

        .hero-bg::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(135deg, rgba(30, 30, 30, 0.7) 0%, rgba(255, 122, 61, 0.1) 100%);
            z-index: 1;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        /* Hero Section Styles */
        .hero-section {
            background-image: linear-gradient(to right, rgba(0, 0, 0, 0.8), rgba(0, 0, 0, 0.4)), url('https://readdy.ai/api/search-image?query=logistics%20warehouse%20with%20shipping%20containers%2C%20cranes%2C%20and%20cargo%20ships%20at%20sunset%20with%20warm%20golden%20light%2C%20professional%20photography%2C%20industrial%20scene%2C%20global%20trade%2C%20import%20export%20business%2C%20high%20quality%2C%20detailed%2C%20realistic%2C%20cinematic%20lighting&width=1920&height=1080&seq=logistics001&orientation=landscape');
            background-size: cover;
            background-position: center;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.6s ease-in-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .hover-scale {
            transition: transform 0.3s ease;
        }
        
        .hover-scale:hover {
            transform: scale(1.05);
        }

        /* Testimonial Slider Styles */
        .testimonial-container {
            position: relative;
            overflow: hidden;
        }

        .testimonial-slide {
            display: none;
            opacity: 0;
            transition: opacity 0.5s ease-in-out;
        }

        .testimonial-slide.active {
            display: block;
            opacity: 1;
        }

        .testimonial-dot {
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .testimonial-dot:hover {
            background-color: #ff7a3d !important;
        }

        .testimonial-dot.active {
            background-color: #ff7a3d !important;
        }

        /* Logo Styling */
        .logo-container {
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }

        .logo-container .text-transparent {
            background: linear-gradient(135deg, #FF7A3D 0%, #FF8C42 50%, #FF7A3D 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            background-size: 200% 200%;
            animation: gradientShift 3s ease-in-out infinite;
        }

        @keyframes gradientShift {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .group:hover .logo-container .text-transparent {
            background: linear-gradient(135deg, #FF8C42 0%, #FF7A3D 50%, #FF8C42 100%);
            -webkit-background-clip: text;
            background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 2s ease-in-out infinite;
        }

        /* Responsive logo adjustments */
        @media (max-width: 768px) {
            .logo-container {
                transform: scale(0.9);
            }
        }

        /* Smooth Scroll and Animation Enhancements */
        html {
            scroll-behavior: smooth;
        }

        /* Lazy Loading Image Styles */
        img.lazy {
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        img.fade-in {
            opacity: 1;
        }

        /* Loading Animation */
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .animate-spin {
            animation: spin 1s linear infinite;
        }

        /* Form Enhancement Styles */
        .form-loading {
            position: relative;
            overflow: hidden;
        }

        .form-loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 122, 61, 0.2), transparent);
            animation: loading-shimmer 1.5s infinite;
        }

        @keyframes loading-shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Enhanced FAQ Animation */
        .faq-answer {
            transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
            overflow: hidden;
        }

        .faq-answer.hidden {
            max-height: 0 !important;
            opacity: 0;
        }

        /* Back to Top Button */
        #backToTop {
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            background: rgba(255, 122, 61, 0.9);
        }

        #backToTop:hover {
            background: rgba(255, 122, 61, 1);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(255, 122, 61, 0.3);
        }

        /* Notification Styles */
        .notification {
            backdrop-filter: blur(10px);
            border-left: 4px solid;
        }

        .notification.success {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }

        .notification.error {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }

        /* Enhanced Form Field Styles */
        input:focus, textarea:focus, select:focus {
            outline: none;
            border-color: #FF7A3D;
            box-shadow: 0 0 0 3px rgba(255, 122, 61, 0.1);
            transition: all 0.2s ease;
        }

        /* Scroll-triggered animations */
        .scroll-animate {
            opacity: 0;
            transform: translateY(30px);
            transition: all 0.6s ease;
        }

        .scroll-animate.visible {
            opacity: 1;
            transform: translateY(0);
        }

        /* Enhanced button hover effects */
        .btn-enhanced {
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .btn-enhanced::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .btn-enhanced:hover::before {
            left: 100%;
        }

        /* Page Transition and Skeleton Loading Styles */
        @keyframes skeleton-loading {
            0% {
                background-position: -200% 0;
            }
            100% {
                background-position: 200% 0;
            }
        }

        .skeleton-placeholder {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: skeleton-loading 1.5s ease-in-out infinite;
        }

        /* Page loader styles */
        #page-loader {
            backdrop-filter: blur(2px);
            background: rgba(255, 255, 255, 0.95);
        }

        /* Enhanced loading spinner */
        .loading-spinner {
            border: 2px solid #f3f3f3;
            border-top: 2px solid #FF7A3D;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            animation: spin 1s linear infinite;
        }

        /* Smooth page transitions */
        body {
            transition: opacity 0.3s ease-in-out;
        }

        .page-transition-enter {
            opacity: 0;
            transform: translateY(20px);
        }

        .page-transition-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
        }

        /* Enhanced Smooth Scrolling Styles */
        body.scrolling {
            cursor: grabbing;
        }

        /* Scroll progress indicator */
        .scroll-progress {
            position: fixed;
            top: 0;
            left: 0;
            height: 3px;
            background: linear-gradient(90deg, #FF7A3D, #E6692F);
            z-index: 9999;
            transition: width 0.1s ease-out;
            box-shadow: 0 0 10px rgba(255, 122, 61, 0.5);
        }

        /* Section highlight animation */
        .section-highlight {
            position: relative;
            overflow: hidden;
        }

        .section-highlight::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 122, 61, 0.1), transparent);
            transition: left 0.6s ease;
            pointer-events: none;
        }

        .section-highlight.active::before {
            left: 100%;
        }

        /* Enhanced back to top button */
        #backToTop {
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(255, 122, 61, 0.3);
        }

        #backToTop:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 6px 25px rgba(255, 122, 61, 0.4);
        }

        /* Keyboard navigation indicator */
        .keyboard-nav-hint {
            position: fixed;
            bottom: 20px;
            left: 20px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 6px;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.3s ease;
            z-index: 1000;
        }

        .keyboard-nav-hint.show {
            opacity: 1;
        }

        /* Breadcrumb Navigation Styles */
        .breadcrumb-nav {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-bottom: 1px solid #dee2e6;
        }

        .breadcrumb-nav ol {
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .breadcrumb-nav li {
            display: flex;
            align-items: center;
        }

        .breadcrumb-nav a {
            text-decoration: none;
            transition: all 0.2s ease;
            padding: 4px 8px;
            border-radius: 4px;
        }

        .breadcrumb-nav a:hover {
            background-color: rgba(255, 122, 61, 0.1);
            transform: translateY(-1px);
        }

        .breadcrumb-nav .ri-arrow-right-s-line {
            font-size: 16px;
            color: #6c757d;
        }

        /* Breadcrumb responsive design */
        @media (max-width: 640px) {
            .breadcrumb-nav {
                padding: 8px 0;
            }

            .breadcrumb-nav ol {
                flex-wrap: wrap;
                gap: 4px;
            }

            .breadcrumb-nav .ri-arrow-right-s-line {
                margin: 0 4px;
            }
        }

        /* Breadcrumb animation */
        .breadcrumb-nav li {
            opacity: 0;
            transform: translateX(-10px);
            animation: breadcrumbSlideIn 0.3s ease forwards;
        }

        .breadcrumb-nav li:nth-child(1) { animation-delay: 0.1s; }
        .breadcrumb-nav li:nth-child(2) { animation-delay: 0.2s; }
        .breadcrumb-nav li:nth-child(3) { animation-delay: 0.3s; }
        .breadcrumb-nav li:nth-child(4) { animation-delay: 0.4s; }
        .breadcrumb-nav li:nth-child(5) { animation-delay: 0.5s; }

        @keyframes breadcrumbSlideIn {
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        /* Search Functionality Styles */
        .search-suggestions {
            max-height: 300px;
            overflow-y: auto;
            scrollbar-width: thin;
            scrollbar-color: #FF7A3D #f1f1f1;
        }

        .search-suggestions::-webkit-scrollbar {
            width: 6px;
        }

        .search-suggestions::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .search-suggestions::-webkit-scrollbar-thumb {
            background: #FF7A3D;
            border-radius: 3px;
        }

        .search-suggestions::-webkit-scrollbar-thumb:hover {
            background: #E6692F;
        }

        /* Header search input focus styles */
        #header-search:focus {
            background: white;
            color: #1f2937;
        }

        #header-search:focus::placeholder {
            color: #6b7280;
        }

        #header-search:focus + button {
            color: #6b7280;
        }

        /* Search result highlighting */
        mark {
            background-color: #fef3c7;
            color: #92400e;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 500;
        }

        /* Search keyboard shortcut hint */
        .search-shortcut-hint {
            position: absolute;
            right: 12px;
            top: 50%;
            transform: translateY(-50%);
            font-size: 11px;
            color: rgba(255, 255, 255, 0.5);
            pointer-events: none;
        }

        /* Responsive search */
        @media (max-width: 1024px) {
            .search-container {
                display: none;
            }
        }
    </style>
</head>
<body class="bg-white">
    <!-- Header -->
    <header class="bg-secondary text-white shadow-md sticky top-0 z-50">
        <div class="container mx-auto px-4 py-3 flex justify-between items-center">
            <div class="flex items-center">
                <a href="index.php" class="flex items-center group">
                    <div class="logo-container flex flex-col leading-none">
                        <div class="text-primary font-bold text-2xl md:text-3xl tracking-wide bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent group-hover:from-orange-500 group-hover:to-primary transition-all duration-300">
                            PROMTECH
                        </div>
                        <div class="text-white text-xs md:text-sm font-medium tracking-widest opacity-90 group-hover:opacity-100 transition-opacity duration-300">
                            EXPORT & IMPORT
                        </div>
                    </div>
                </a>
            </div>
            <nav class="hidden md:flex items-center space-x-6">
                    <?php foreach ($navigation_menu as $key => $item): ?>
                        <?php if (isset($item['submenu'])): ?>
                            <!-- Dropdown Menu -->
                            <div class="dropdown relative">
                                <button class="flex items-center hover:text-primary py-2">
                                    <?php echo $item['title']; ?>
                                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                                        <i class="ri-arrow-down-s-line"></i>
                                    </div>
                                </button>
                                <div class="dropdown-menu">
                                    <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                        <a href="<?php echo $sub_item['url']; ?>">
                                            <?php echo $sub_item['title']; ?>
                                        </a>
                                    <?php endforeach; ?>
                                </div>
                            </div>
                        <?php else: ?>
                            <!-- Regular Menu Item -->
                            <a href="<?php echo $item['url']; ?>"
                               class="hover:text-primary py-2 inline-block">
                                <?php echo $item['title']; ?>
                            </a>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </nav>

                <!-- Search Box -->
                <div class="hidden lg:block relative">
                    <form action="search.php" method="GET" class="relative">
                        <input
                            type="text"
                            name="q"
                            placeholder="Search..."
                            class="w-64 px-4 py-2 pl-10 bg-white/10 border border-white/20 rounded-full text-white placeholder-white/70 focus:outline-none focus:ring-2 focus:ring-primary focus:bg-white focus:text-gray-900 focus:placeholder-gray-500 transition-all"
                            id="header-search"
                        >
                        <button type="submit" class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/70 hover:text-white transition-colors">
                            <i class="ri-search-line"></i>
                        </button>
                    </form>
                </div>

            <div class="hidden md:block">
                <a href="quote.php" class="bg-primary text-white px-4 py-2 rounded-button flex items-center whitespace-nowrap">
                    Get A Quote
                    <div class="w-4 h-4 flex items-center justify-center ml-1">
                        <i class="ri-arrow-right-line"></i>
                    </div>
                </a>
            </div>
            <button class="md:hidden w-10 h-10 flex items-center justify-center text-white hover:text-primary transition-colors" id="mobile-menu-btn">
                <i class="ri-menu-line text-2xl"></i>
            </button>
        </div>
    </header>

    <!-- Mobile Menu Overlay -->
    <div class="mobile-menu-overlay" id="mobile-menu-overlay"></div>

    <!-- Mobile Menu -->
    <div class="mobile-menu" id="mobile-menu">
        <div class="mobile-menu-header">
            <div class="flex items-center">
                <div class="logo-container flex flex-col leading-none">
                    <div class="text-primary font-bold text-xl tracking-wide bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent">
                        PROMTECH
                    </div>
                    <div class="text-gray-800 text-xs font-medium tracking-widest opacity-90">
                        EXPORT & IMPORT
                    </div>
                </div>
            </div>
            <button class="mobile-menu-close" id="mobile-menu-close">
                <i class="ri-close-line"></i>
            </button>
        </div>

        <nav class="mobile-menu-nav">
            <?php foreach ($navigation_menu as $key => $item): ?>
                <?php if (isset($item['submenu'])): ?>
                    <!-- Mobile Dropdown -->
                    <div class="mobile-menu-item">
                        <button class="mobile-dropdown-btn" data-dropdown="<?php echo $key; ?>">
                            <?php echo $item['title']; ?>
                            <i class="ri-arrow-down-s-line transition-transform duration-200" id="icon-<?php echo $key; ?>"></i>
                        </button>
                        <div class="mobile-submenu" id="submenu-<?php echo $key; ?>">
                            <?php foreach ($item['submenu'] as $sub_key => $sub_item): ?>
                                <a href="<?php echo $sub_item['url']; ?>">
                                    <?php echo $sub_item['title']; ?>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    </div>
                <?php else: ?>
                    <div class="mobile-menu-item">
                        <a href="<?php echo $item['url']; ?>" class="mobile-menu-link <?php echo $item['active'] ? 'text-primary' : ''; ?>">
                            <?php echo $item['title']; ?>
                        </a>
                    </div>
                <?php endif; ?>
            <?php endforeach; ?>

            <div class="mobile-menu-item" style="border-bottom: none; padding: 20px;">
                <a href="quote.php" class="bg-primary text-white px-6 py-3 rounded-button text-center block font-semibold">
                    Get A Quote
                </a>
            </div>
        </nav>
    </div>
