<?php
// Test email functionality
error_reporting(E_ALL);
ini_set('display_errors', 1);

require_once 'config.php';

echo "<h1>PROMTECH Email Functionality Test</h1>\n";

// Test 1: Check if PHPMailer files exist
echo "<h2>Test 1: PHPMailer Files</h2>\n";
$phpmailerPath = __DIR__ . '/vendor/PHPMailer/PHPMailer.php';
$smtpPath = __DIR__ . '/vendor/PHPMailer/SMTP.php';
$exceptionPath = __DIR__ . '/vendor/PHPMailer/Exception.php';

echo "PHPMailer.php exists: " . (file_exists($phpmailerPath) ? "✅ Yes" : "❌ No") . "<br>\n";
echo "SMTP.php exists: " . (file_exists($smtpPath) ? "✅ Yes" : "❌ No") . "<br>\n";
echo "Exception.php exists: " . (file_exists($exceptionPath) ? "✅ Yes" : "❌ No") . "<br>\n";

// Test 2: Try to include PHPMailer classes
echo "<h2>Test 2: Include PHPMailer Classes</h2>\n";
try {
    require_once $phpmailerPath;
    require_once $smtpPath;
    require_once $exceptionPath;
    echo "✅ PHPMailer classes included successfully<br>\n";
} catch (Exception $e) {
    echo "❌ Error including PHPMailer: " . $e->getMessage() . "<br>\n";
}

use PHPMailer\PHPMailer\PHPMailer;
use PHPMailer\PHPMailer\Exception;

// Test 3: Check PHPMailer class
echo "<h2>Test 3: PHPMailer Class Check</h2>\n";
if (class_exists('PHPMailer\PHPMailer\PHPMailer')) {
    echo "✅ PHPMailer class loaded successfully<br>\n";
} else {
    echo "❌ PHPMailer class not found<br>\n";
}

// Test 4: Check SMTP configuration
echo "<h2>Test 4: SMTP Configuration</h2>\n";
echo "SMTP Host: " . SMTP_HOST . "<br>\n";
echo "SMTP Port: " . SMTP_PORT . "<br>\n";
echo "SMTP Username: " . SMTP_USERNAME . "<br>\n";
echo "SMTP Security: " . SMTP_SECURE . "<br>\n";
echo "Site Email: " . SITE_EMAIL . "<br>\n";

// Test 5: Create PHPMailer instance
echo "<h2>Test 5: Create PHPMailer Instance</h2>\n";
try {
    $mail = new PHPMailer(true);
    echo "✅ PHPMailer instance created successfully<br>\n";
} catch (Exception $e) {
    echo "❌ Error creating PHPMailer instance: " . $e->getMessage() . "<br>\n";
    exit;
}

// Test 6: Test SMTP connection (without sending)
echo "<h2>Test 6: SMTP Connection Test</h2>\n";
try {
    $mail->isSMTP();
    $mail->Host = SMTP_HOST;
    $mail->SMTPAuth = true;
    $mail->Username = SMTP_USERNAME;
    $mail->Password = SMTP_PASSWORD;
    $mail->SMTPSecure = SMTP_SECURE;
    $mail->Port = SMTP_PORT;
    $mail->SMTPDebug = 2;
    $mail->Debugoutput = 'html';

    echo "SMTP configuration set. Testing connection...<br>\n";

    // Try to connect to SMTP server
    if ($mail->smtpConnect()) {
        echo "✅ SMTP connection successful!<br>\n";
        $mail->smtpClose();
    } else {
        echo "❌ SMTP connection failed<br>\n";
    }

} catch (Exception $e) {
    echo "❌ SMTP connection error: " . $e->getMessage() . "<br>\n";
}

echo "<h2>Test Complete</h2>\n";
?>
