    <!-- Footer -->
    <footer class="bg-gray-50 text-gray-800 pt-16 pb-8">
        <div class="container mx-auto px-4">
            <!-- Newsletter Section -->
            <div class="bg-primary rounded-2xl p-8 mb-12 text-center relative overflow-hidden" style="background-image: linear-gradient(rgba(255, 122, 61, 0.9), rgba(255, 122, 61, 0.9)), url('<?php echo getAssetUrl('assets/img/img5.webp'); ?>'); background-size: cover; background-position: center;">
                <h3 class="text-2xl font-bold text-white mb-4">Subscribe Our Newsletter</h3>
                <p class="text-white/90 mb-6 max-w-2xl mx-auto">
                    Stay updated with the latest logistics trends, shipping updates, and industry insights. Get exclusive offers and expert tips delivered to your inbox.
                </p>
                <form id="newsletterForm" class="flex flex-col sm:flex-row gap-4 max-w-md mx-auto" data-ajax="true">
                    <input type="email" name="email" placeholder="Enter your email address" required
                           class="flex-1 px-4 py-3 rounded-lg text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-white transition-all duration-300">
                    <button type="submit" class="bg-white text-primary px-6 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-all duration-300 btn-enhanced">
                        Subscribe
                    </button>
                </form>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-12">
                <!-- Company Info -->
                <div>
                    <div class="flex items-center mb-6">
                        <div class="logo-container flex flex-col leading-none">
                            <div class="text-primary font-bold text-2xl tracking-wide bg-gradient-to-r from-primary to-orange-500 bg-clip-text text-transparent">
                                PROMTECH
                            </div>
                            <div class="text-gray-800 text-xs font-medium tracking-widest opacity-90">
                                EXPORT & IMPORT
                            </div>
                        </div>
                    </div>
                    <p class="text-gray-600 mb-6 leading-relaxed">
                        Leading platform for global trade solutions, connecting markets and empowering businesses worldwide with reliable logistics services.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-facebook-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-twitter-x-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-linkedin-fill"></i>
                        </a>
                        <a href="#" class="w-10 h-10 bg-gray-200 text-gray-600 rounded-full flex items-center justify-center hover:bg-primary hover:text-white transition-colors">
                            <i class="ri-instagram-line"></i>
                        </a>
                    </div>
                </div>
                
                <!-- Company -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Company</h3>
                    <ul class="space-y-3">
                        <li><a href="about.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>About Us
                        </a></li>
                        <li><a href="services.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Services
                        </a></li>
                        <li><a href="commodities.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Commodities
                        </a></li>
                        <li><a href="#" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Our Team
                        </a></li>
                        <li><a href="contact.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Contact Us
                        </a></li>
                    </ul>
                </div>
                
                <!-- Services -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Services</h3>
                    <ul class="space-y-3">
                        <li><a href="ocean-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Ocean Freight
                        </a></li>
                        <li><a href="air-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Air Freight
                        </a></li>
                        <li><a href="road-freight.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Road Freight
                        </a></li>
                        <li><a href="warehousing.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Warehousing
                        </a></li>
                        <li><a href="custom-clearance.php" class="text-gray-600 hover:text-primary transition-colors flex items-center">
                            <i class="ri-arrow-right-s-line text-primary mr-2"></i>Custom Clearance
                        </a></li>
                    </ul>
                </div>
                
                <!-- Contact Info -->
                <div>
                    <h3 class="text-xl font-bold mb-6">Contact Info</h3>
                    <div class="space-y-4">
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-map-pin-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600 leading-relaxed">74 Hrushevsky Street,<br>Kiev, 01008, Ukraine</p>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-mail-line"></i>
                            </div>
                            <div>
                                <a href="mailto:<EMAIL>" class="text-gray-600 hover:text-primary transition-colors">
                                    <EMAIL>
                                </a>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-phone-line"></i>
                            </div>
                            <div>
                                <a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>" class="text-gray-600 hover:text-primary transition-colors">
                                    <?php echo COMPANY_PHONE; ?>
                                </a>
                            </div>
                        </div>
                        <div class="flex items-start space-x-3">
                            <div class="w-5 h-5 text-primary mt-1">
                                <i class="ri-time-line"></i>
                            </div>
                            <div>
                                <p class="text-gray-600">Mon - Fri: 9:00 AM - 6:00 PM<br>Saturday: 9:00 AM - 4:00 PM</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Footer Bottom -->
            <div class="border-t border-gray-300 pt-8">
                <div class="flex flex-col md:flex-row justify-between items-center">
                    <p class="text-gray-600 mb-4 md:mb-0">
                        © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. All rights reserved.
                    </p>
                    <div class="flex space-x-6">
                        <a href="privacy-policy.php" class="text-gray-600 hover:text-primary transition-colors">Privacy Policy</a>
                        <a href="terms-of-service.php" class="text-gray-600 hover:text-primary transition-colors">Terms of Service</a>
                        <a href="disclaimer.php" class="text-gray-600 hover:text-primary transition-colors">Disclaimer</a>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <!-- Back to Top Button -->
    <button id="backToTop" class="fixed bottom-6 right-6 w-12 h-12 rounded-full text-white shadow-lg z-50 opacity-0 pointer-events-none transition-all duration-300 flex items-center justify-center">
        <i class="ri-arrow-up-line text-xl"></i>
    </button>

    <!-- JavaScript -->
    <script>
        // Mobile menu functionality
        document.addEventListener('DOMContentLoaded', function() {
            console.log('Mobile menu script loaded');
            const mobileMenuBtn = document.getElementById('mobile-menu-btn');
            const mobileMenu = document.getElementById('mobile-menu');
            const mobileMenuOverlay = document.getElementById('mobile-menu-overlay');
            const mobileMenuClose = document.getElementById('mobile-menu-close');

            console.log('Mobile menu elements:', {
                btn: mobileMenuBtn,
                menu: mobileMenu,
                overlay: mobileMenuOverlay,
                close: mobileMenuClose
            });

            // Open mobile menu
            if (mobileMenuBtn) {
                console.log('Adding click listener to mobile menu button');
                mobileMenuBtn.addEventListener('click', function() {
                    console.log('Mobile menu button clicked');
                    if (mobileMenu) {
                        console.log('Adding active class to mobile menu');
                        mobileMenu.classList.add('active');
                    }
                    if (mobileMenuOverlay) {
                        console.log('Adding active class to mobile menu overlay');
                        mobileMenuOverlay.classList.add('active');
                    }
                    document.body.style.overflow = 'hidden';
                });
            } else {
                console.log('Mobile menu button not found');
            }

            // Close mobile menu
            function closeMobileMenu() {
                if (mobileMenu) {
                    mobileMenu.classList.remove('active');
                }
                if (mobileMenuOverlay) {
                    mobileMenuOverlay.classList.remove('active');
                }
                document.body.style.overflow = '';
            }

            if (mobileMenuClose) {
                mobileMenuClose.addEventListener('click', closeMobileMenu);
            }

            if (mobileMenuOverlay) {
                mobileMenuOverlay.addEventListener('click', closeMobileMenu);
            }

            // Desktop dropdown functionality
            const dropdowns = document.querySelectorAll('.dropdown');
            dropdowns.forEach(dropdown => {
                const button = dropdown.querySelector('button');
                const menu = dropdown.querySelector('.dropdown-menu');
                let hoverTimeout;

                if (button && menu) {
                    // Show dropdown on hover
                    dropdown.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                        menu.style.display = 'block';
                        setTimeout(() => {
                            menu.style.opacity = '1';
                            menu.style.transform = 'translateY(0)';
                        }, 10);
                    });

                    // Hide dropdown with delay
                    dropdown.addEventListener('mouseleave', function() {
                        hoverTimeout = setTimeout(() => {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            setTimeout(() => {
                                menu.style.display = 'none';
                            }, 300);
                        }, 100);
                    });

                    // Keep dropdown open when hovering over menu
                    menu.addEventListener('mouseenter', function() {
                        clearTimeout(hoverTimeout);
                    });

                    menu.addEventListener('mouseleave', function() {
                        hoverTimeout = setTimeout(() => {
                            menu.style.opacity = '0';
                            menu.style.transform = 'translateY(-10px)';
                            setTimeout(() => {
                                menu.style.display = 'none';
                            }, 300);
                        }, 100);
                    });
                }
            });

            // Mobile dropdown toggles
            document.querySelectorAll('.mobile-dropdown-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const dropdownKey = this.getAttribute('data-dropdown');
                    const submenu = document.getElementById('submenu-' + dropdownKey);
                    const icon = document.getElementById('icon-' + dropdownKey);

                    if (submenu && icon) {
                        const isActive = submenu.classList.contains('active');

                        // Close all other submenus
                        document.querySelectorAll('.mobile-submenu').forEach(menu => {
                            menu.classList.remove('active');
                        });
                        document.querySelectorAll('.mobile-dropdown-btn i').forEach(i => {
                            i.classList.remove('ri-arrow-up-s-line');
                            i.classList.add('ri-arrow-down-s-line');
                            i.style.transform = 'rotate(0deg)';
                        });

                        // Toggle current submenu
                        if (!isActive) {
                            submenu.classList.add('active');
                            icon.classList.remove('ri-arrow-down-s-line');
                            icon.classList.add('ri-arrow-up-s-line');
                            icon.style.transform = 'rotate(180deg)';
                        }
                    }
                });
            });

            // Close mobile menu when clicking on menu links
            document.querySelectorAll('.mobile-menu-link, .mobile-submenu a').forEach(link => {
                link.addEventListener('click', function() {
                    closeMobileMenu();
                });
            });

            // Improve desktop dropdown behavior
            let dropdownTimeout;
            document.querySelectorAll('.dropdown').forEach(dropdown => {
                const menu = dropdown.querySelector('.dropdown-menu');

                dropdown.addEventListener('mouseenter', function() {
                    clearTimeout(dropdownTimeout);
                    menu.style.display = 'block';
                    setTimeout(() => {
                        menu.style.opacity = '1';
                        menu.style.transform = 'translateY(0)';
                    }, 10);
                });

                dropdown.addEventListener('mouseleave', function() {
                    dropdownTimeout = setTimeout(() => {
                        menu.style.opacity = '0';
                        menu.style.transform = 'translateY(-10px)';
                        setTimeout(() => {
                            menu.style.display = 'none';
                        }, 300);
                    }, 100);
                });
            });


        
        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            });
        });
        
        // Add animation classes on scroll
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };
        
        const observer = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                }
            });
        }, observerOptions);
        
        // Observe elements for animation
        document.querySelectorAll('.hover-scale, .bg-white').forEach(el => {
            observer.observe(el);
        });

        // Simple animations and interactions can be added here if needed

        // Testimonial Slider Functionality
        let currentTestimonial = 0;
        const testimonialSlides = document.querySelectorAll('.testimonial-slide');
        const testimonialDots = document.querySelectorAll('.testimonial-dot');

        function showTestimonial(index) {
            // Hide all slides
            testimonialSlides.forEach(slide => {
                slide.classList.remove('active');
            });

            // Remove active class from all dots
            testimonialDots.forEach(dot => {
                dot.classList.remove('active');
                dot.classList.add('bg-gray-300');
                dot.classList.remove('bg-primary');
            });

            // Show current slide
            if (testimonialSlides[index]) {
                testimonialSlides[index].classList.add('active');
            }

            // Activate current dot
            if (testimonialDots[index]) {
                testimonialDots[index].classList.add('active');
                testimonialDots[index].classList.remove('bg-gray-300');
                testimonialDots[index].classList.add('bg-primary');
            }
        }

        // Add click event listeners to dots
        testimonialDots.forEach((dot, index) => {
            dot.addEventListener('click', () => {
                currentTestimonial = index;
                showTestimonial(currentTestimonial);
            });
        });

        // Auto-advance testimonials every 5 seconds
        setInterval(() => {
            currentTestimonial = (currentTestimonial + 1) % testimonialSlides.length;
            showTestimonial(currentTestimonial);
        }, 5000);

        // Initialize first testimonial
        if (testimonialSlides.length > 0) {
            showTestimonial(0);
        }

        // Smooth Scroll Animation for Navigation Links
        function initializeSmoothScroll() {
            // Add smooth scrolling to all anchor links that point to sections on the same page
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Add smooth scroll for "back to top" functionality
            const backToTopBtn = document.createElement('button');
            backToTopBtn.innerHTML = '<i class="ri-arrow-up-line"></i>';
            backToTopBtn.className = 'fixed bottom-6 right-6 bg-primary text-white p-3 rounded-full shadow-lg hover:bg-secondary transition-all duration-300 opacity-0 pointer-events-none z-50';
            backToTopBtn.id = 'backToTop';
            document.body.appendChild(backToTopBtn);

            // Show/hide back to top button
            window.addEventListener('scroll', function() {
                if (window.pageYOffset > 300) {
                    backToTopBtn.classList.remove('opacity-0', 'pointer-events-none');
                    backToTopBtn.classList.add('opacity-100');
                } else {
                    backToTopBtn.classList.add('opacity-0', 'pointer-events-none');
                    backToTopBtn.classList.remove('opacity-100');
                }
            });

            // Back to top functionality
            backToTopBtn.addEventListener('click', function() {
                window.scrollTo({
                    top: 0,
                    behavior: 'smooth'
                });
            });
        }

        // Image Lazy Loading
        function initializeLazyLoading() {
            const images = document.querySelectorAll('img[data-src]');

            if ('IntersectionObserver' in window) {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            img.src = img.dataset.src;
                            img.classList.remove('lazy');
                            img.classList.add('fade-in');
                            imageObserver.unobserve(img);
                        }
                    });
                });

                images.forEach(img => imageObserver.observe(img));
            } else {
                // Fallback for older browsers
                images.forEach(img => {
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                });
            }
        }

        // Enhanced Form Handling with Loading States and Error Handling
        function initializeFormEnhancements() {
            const forms = document.querySelectorAll('form');

            forms.forEach(form => {
                const submitBtn = form.querySelector('button[type="submit"], input[type="submit"]');

                if (submitBtn) {
                    const originalText = submitBtn.textContent || submitBtn.value;

                    form.addEventListener('submit', function(e) {
                        // Add loading state
                        submitBtn.disabled = true;
                        submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Sending...';
                        submitBtn.classList.add('opacity-75');

                        // Clear previous error messages
                        form.querySelectorAll('.error-message').forEach(error => error.remove());

                        // Basic form validation
                        const requiredFields = form.querySelectorAll('[required]');
                        let hasErrors = false;

                        requiredFields.forEach(field => {
                            if (!field.value.trim()) {
                                hasErrors = true;
                                showFieldError(field, 'This field is required');
                            } else if (field.type === 'email' && !isValidEmail(field.value)) {
                                hasErrors = true;
                                showFieldError(field, 'Please enter a valid email address');
                            }
                        });

                        if (hasErrors) {
                            e.preventDefault();
                            resetSubmitButton(submitBtn, originalText);
                            return false;
                        }

                        // If using AJAX, handle the response
                        if (form.dataset.ajax === 'true') {
                            e.preventDefault();
                            handleAjaxSubmission(form, submitBtn, originalText);
                        }
                    });
                }
            });
        }

        function showFieldError(field, message) {
            const errorDiv = document.createElement('div');
            errorDiv.className = 'error-message text-red-500 text-sm mt-1';
            errorDiv.textContent = message;
            field.parentNode.appendChild(errorDiv);
            field.classList.add('border-red-500');

            // Remove error styling when user starts typing
            field.addEventListener('input', function() {
                field.classList.remove('border-red-500');
                const errorMsg = field.parentNode.querySelector('.error-message');
                if (errorMsg) errorMsg.remove();
            });
        }

        function isValidEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }

        function resetSubmitButton(button, originalText) {
            button.disabled = false;
            button.innerHTML = originalText;
            button.classList.remove('opacity-75');
        }

        function handleAjaxSubmission(form, submitBtn, originalText) {
            const formData = new FormData(form);

            fetch(form.action || window.location.href, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                resetSubmitButton(submitBtn, originalText);

                if (data.success) {
                    showNotification('Success! Your message has been sent.', 'success');
                    form.reset();
                } else {
                    showNotification(data.message || 'An error occurred. Please try again.', 'error');
                }
            })
            .catch(error => {
                resetSubmitButton(submitBtn, originalText);
                showNotification('Network error. Please check your connection and try again.', 'error');
            });
        }

        function showNotification(message, type) {
            const notification = document.createElement('div');
            notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 transition-all duration-300 transform translate-x-full ${
                type === 'success' ? 'bg-green-500 text-white' : 'bg-red-500 text-white'
            }`;
            notification.innerHTML = `
                <div class="flex items-center">
                    <i class="ri-${type === 'success' ? 'check' : 'error-warning'}-line mr-2"></i>
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="this.parentElement.parentElement.remove()">
                        <i class="ri-close-line"></i>
                    </button>
                </div>
            `;

            document.body.appendChild(notification);

            // Animate in
            setTimeout(() => {
                notification.classList.remove('translate-x-full');
            }, 100);

            // Auto remove after 5 seconds
            setTimeout(() => {
                notification.classList.add('translate-x-full');
                setTimeout(() => notification.remove(), 300);
            }, 5000);
        }

        // FAQ Accordion Functionality
        function initializeFAQ() {
            const faqQuestions = document.querySelectorAll('.faq-question');

            if (faqQuestions.length === 0) {
                return;
            }

            faqQuestions.forEach((question, index) => {
                // Remove any existing event listeners
                question.replaceWith(question.cloneNode(true));
                const newQuestion = document.querySelectorAll('.faq-question')[index];

                newQuestion.addEventListener('click', function(e) {
                    e.preventDefault();
                    e.stopPropagation();

                    const faqItem = this.closest('.faq-item');
                    const answer = faqItem.querySelector('.faq-answer');
                    const icon = this.querySelector('.faq-icon');

                    if (!answer || !icon) {
                        return;
                    }

                    // Close all other FAQs first
                    document.querySelectorAll('.faq-item').forEach(item => {
                        if (item !== faqItem) {
                            const otherAnswer = item.querySelector('.faq-answer');
                            const otherIcon = item.querySelector('.faq-icon');
                            if (otherAnswer && otherIcon) {
                                otherAnswer.classList.add('hidden');
                                otherIcon.classList.remove('ri-arrow-up-line');
                                otherIcon.classList.add('ri-arrow-down-line');
                            }
                        }
                    });

                    // Toggle current FAQ with smooth animation
                    const isOpen = !answer.classList.contains('hidden');

                    if (isOpen) {
                        answer.style.maxHeight = answer.scrollHeight + 'px';
                        setTimeout(() => {
                            answer.style.maxHeight = '0px';
                            setTimeout(() => {
                                answer.classList.add('hidden');
                                answer.style.maxHeight = '';
                            }, 300);
                        }, 10);
                        icon.classList.remove('ri-arrow-up-line');
                        icon.classList.add('ri-arrow-down-line');
                    } else {
                        answer.classList.remove('hidden');
                        answer.style.maxHeight = '0px';
                        setTimeout(() => {
                            answer.style.maxHeight = answer.scrollHeight + 'px';
                            setTimeout(() => {
                                answer.style.maxHeight = '';
                            }, 300);
                        }, 10);
                        icon.classList.remove('ri-arrow-down-line');
                        icon.classList.add('ri-arrow-up-line');
                    }
                });
            });
        }

        // Initialize all functionality with a small delay to ensure DOM is ready
        setTimeout(() => {
            initializeSmoothScroll();
            initializeLazyLoading();
            initializeFormEnhancements();
            initializeFAQ();
        }, 100);

        }); // Close main DOMContentLoaded event listener

    </script>
</body>
</html>
