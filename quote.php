<?php 
require_once 'config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Get Quote - <?php echo COMPANY_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colours: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .hero-banner {
            background: linear-gradient(rgba(30, 58, 138, 0.6), rgba(255, 122, 61, 0.4)), url('<?php echo getAssetUrl('assets/img/img6.webp'); ?>');
            background-size: cover;
            background-position: centre centre;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        .rounded-button {
            border-radius: 25px;
        }
        .form-input {
            transition: all 0.3s ease;
        }
        .form-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 122, 61, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>

    <!-- Hero Banner Section -->
    <section class="hero-banner min-h-[400px] flex items-center justify-center relative">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary/80 to-primary/60"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <nav class="flex items-center justify-center space-x-2 mb-6">
                    <a href="index.php" class="text-white/80 hover:text-white transition-colors">
                        <i class="ri-home-line mr-1"></i>Home
                    </a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <span class="text-white font-medium">Get Quote</span>
                </nav>
                <h1 class="text-5xl md:text-6xl font-bold mb-4">Get Quote</h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto">
                    Request a personalized quote for your logistics needs. Our experts will provide competitive pricing for your shipping requirements.
                </p>
            </div>
        </div>
    </section>

    <!-- Quote Form Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-12 items-start">
                    <!-- Left Column - Form Info -->
                    <div>
                        <div class="mb-8">
                            <span class="text-primary font-semibold text-lg">Request Quote</span>
                            <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">Get Your Shipping Quote Today</h2>
                            <p class="text-gray-600 text-lg leading-relaxed">
                                Fill out our comprehensive quote form and receive a detailed estimate for your logistics needs. 
                                Our team will analyze your requirements and provide competitive pricing within 24 hours.
                            </p>
                        </div>

                        <div class="space-y-6">
                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-time-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Quick Response</h3>
                                    <p class="text-gray-600">Get your quote within 24 hours of submission</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-shield-check-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Accurate Pricing</h3>
                                    <p class="text-gray-600">Transparent and competitive rates with no hidden fees</p>
                                </div>
                            </div>
                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-customer-service-2-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Expert Consultation</h3>
                                    <p class="text-gray-600">Professional advice on the best shipping solutions</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Quote Form -->
                    <div class="bg-gray-50 p-8 rounded-2xl shadow-lg">
                        <h3 class="text-2xl font-bold text-secondary mb-6">Request Your Quote</h3>
                        <form id="quoteForm" class="space-y-6">
                            <!-- Personal Information -->
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                    <input type="text" name="firstName" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                    <input type="text" name="lastName" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>

                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Email Address *</label>
                                    <input type="email" name="email" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                    <input type="tel" name="phone" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                <input type="text" name="company" 
                                       class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <!-- Shipping Details -->
                            <div class="border-t pt-6">
                                <h4 class="text-lg font-semibold text-secondary mb-4">Shipping Details</h4>
                                
                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Origin Country *</label>
                                        <select name="originCountry" required 
                                                class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Select Origin</option>
                                            <option value="Ukraine">Ukraine</option>
                                            <option value="Poland">Poland</option>
                                            <option value="Germany">Germany</option>
                                            <option value="Turkey">Turkey</option>
                                            <option value="Romania">Romania</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Destination *</label>
                                        <select name="destination" required 
                                                class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Select Destination</option>
                                            <option value="Europe">Europe</option>
                                            <option value="Asia">Asia</option>
                                            <option value="Africa">Africa</option>
                                            <option value="Americas">Americas</option>
                                            <option value="Middle East">Middle East</option>
                                            <option value="Other">Other</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="grid md:grid-cols-2 gap-4 mb-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Service Type *</label>
                                        <select name="serviceType" required 
                                                class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                            <option value="">Select Service</option>
                                            <option value="Ocean Freight">Ocean Freight</option>
                                            <option value="Air Freight">Air Freight</option>
                                            <option value="Road Freight">Road Freight</option>
                                            <option value="Rail Freight">Rail Freight</option>
                                            <option value="Warehousing">Warehousing</option>
                                            <option value="Custom Clearance">Custom Clearance</option>
                                        </select>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Cargo Weight (kg)</label>
                                        <input type="number" name="weight" placeholder="e.g., 1000" 
                                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                </div>

                                <div class="grid md:grid-cols-3 gap-4 mb-4">
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Length (cm)</label>
                                        <input type="number" name="length" placeholder="Length" 
                                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Width (cm)</label>
                                        <input type="number" name="width" placeholder="Width" 
                                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                    <div>
                                        <label class="block text-sm font-medium text-gray-700 mb-2">Height (cm)</label>
                                        <input type="number" name="height" placeholder="Height" 
                                               class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    </div>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Commodity Type</label>
                                    <select name="commodityType" 
                                            class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                        <option value="">Select Commodity</option>
                                        <option value="Grain Products">Grain Products</option>
                                        <option value="Wood Products">Wood Products</option>
                                        <option value="Metal Products">Metal Products</option>
                                        <option value="Agricultural Products">Agricultural Products</option>
                                        <option value="Electronics">Electronics</option>
                                        <option value="Textiles">Textiles</option>
                                        <option value="Machinery">Machinery</option>
                                        <option value="Other">Other</option>
                                    </select>
                                </div>

                                <div class="mb-4">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Preferred Pickup Date</label>
                                    <input type="date" name="pickupDate" 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>

                                <div class="mb-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Additional Requirements</label>
                                    <textarea name="requirements" rows="4" placeholder="Please describe any special requirements, handling instructions, or additional services needed..."
                                              class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"></textarea>
                                </div>

                                <button type="submit" 
                                        class="w-full bg-primary text-white py-4 px-8 rounded-button font-semibold text-lg hover:bg-primary/90 transition-colors flex items-center justify-center">
                                    <i class="ri-send-plane-line mr-2"></i>
                                    Request Quote
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <span class="text-primary font-semibold text-lg">Why Choose PROMTECH</span>
                <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">Your Trusted Logistics Partner</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Experience the difference with our comprehensive logistics solutions and dedicated customer service.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                <div class="text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-time-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Fast Quotes</h3>
                    <p class="text-gray-600">Receive detailed quotes within 24 hours</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-money-euro-circle-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Competitive Rates</h3>
                    <p class="text-gray-600">Best pricing in the market with no hidden fees</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-global-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Global Network</h3>
                    <p class="text-gray-600">Worldwide coverage with reliable partners</p>
                </div>
                <div class="text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-customer-service-2-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">24/7 Support</h3>
                    <p class="text-gray-600">Round-the-clock customer assistance</p>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const quoteForm = document.getElementById('quoteForm');
            if (quoteForm) {
                quoteForm.addEventListener('submit', async function(e) {
                    e.preventDefault(); // Prevent form from submitting normally

                    try {
                        // Get form data
                        const formData = new FormData(this);
                        const data = Object.fromEntries(formData);

                        // Basic validation
                        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'originCountry', 'destination', 'serviceType'];
                        const missingFields = requiredFields.filter(field => !data[field]);

                        if (missingFields.length > 0) {
                            throw new Error('Please fill in all required fields: ' + missingFields.join(', '));
                        }

                        // Email validation
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(data.email)) {
                            throw new Error('Please enter a valid email address');
                        }

                        // Submit form
                        const submitBtn = this.querySelector('button[type="submit"]');
                        const originalText = submitBtn.innerHTML;
                        submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Sending...';
                        submitBtn.disabled = true;

                        // Send to PHP handler
                        const response = await fetch('<?php echo getAssetUrl('process-form.php'); ?>?type=quote', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(data)
                        });

                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }

                        const result = await response.json();

                        // Create notification element
                        const notification = document.createElement('div');
                        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg ${result.success ? 'bg-green-500' : 'bg-red-500'} text-white max-w-md z-50 flex items-center`;
                        notification.innerHTML = `
                            <div class="flex-1">${result.message}</div>
                            <button class="ml-4 hover:text-gray-200" onclick="this.parentElement.remove()">
                                <i class="ri-close-line"></i>
                            </button>
                        `;
                        document.body.appendChild(notification);

                        // Auto remove after 5 seconds
                        setTimeout(() => {
                            notification.remove();
                        }, 5000);

                        if (result.success) {
                            this.reset();
                        }

                        // Reset button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;

                    } catch (error) {
                        console.error('Error:', error);

                        // Create error notification
                        const notification = document.createElement('div');
                        notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg bg-red-500 text-white max-w-md z-50 flex items-center';
                        notification.innerHTML = `
                            <div class="flex-1">${error.message || 'There was an error submitting your request. Please try again or contact us directly.'}</div>
                            <button class="ml-4 hover:text-gray-200" onclick="this.parentElement.remove()">
                                <i class="ri-close-line"></i>
                            </button>
                        `;
                        document.body.appendChild(notification);

                        // Auto remove after 5 seconds
                        setTimeout(() => {
                            notification.remove();
                        }, 5000);

                        // Reset button state
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                });
            }
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
