<?php

require_once 'config.php';

// Include PHPMailer classes
require_once __DIR__ . '/vendor/PHPMailer.php';
require_once __DIR__ . '/vendor/SMTP.php';
require_once __DIR__ . '/vendor/Exception.php';
use <PERSON><PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

// Set content type to JSON
header('Content-Type: application/json');

// Enable CORS if needed
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST');
header('Access-Control-Allow-Headers: Content-Type');

// Only allow POST requests
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => 'Method not allowed']);
    exit;
}

try {
    // Get POST data
    $input = json_decode(file_get_contents('php://input'), true);
    
    // If JSON decode fails, try regular POST data
    if (!$input) {
        $input = $_POST;
    }
    
    // Validate email
    if (empty($input['email'])) {
        echo json_encode(['success' => false, 'message' => 'Email address is required']);
        exit;
    }
    
    // Validate email format
    if (!filter_var($input['email'], FILTER_VALIDATE_EMAIL)) {
        echo json_encode(['success' => false, 'message' => 'Please enter a valid email address']);
        exit;
    }
    
    // Sanitize email
    $email = htmlspecialchars(strip_tags(trim($input['email'])), ENT_QUOTES, 'UTF-8');
    
    // Check if email already exists in newsletter list
    $newsletterFile = 'logs/newsletter_subscribers.txt';
    $logDir = dirname($newsletterFile);
    
    // Create logs directory if it doesn't exist
    if (!is_dir($logDir)) {
        mkdir($logDir, 0755, true);
    }
    
    // Check for duplicate subscription
    if (file_exists($newsletterFile)) {
        $subscribers = file($newsletterFile, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        foreach ($subscribers as $subscriber) {
            $subscriberData = json_decode($subscriber, true);
            if ($subscriberData && $subscriberData['email'] === $email) {
                echo json_encode(['success' => true, 'message' => 'You are already subscribed to our newsletter!']);
                exit;
            }
        }
    }
    
    // Add subscriber to file
    $subscriberData = [
        'email' => $email,
        'timestamp' => date('Y-m-d H:i:s'),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ];
    
    file_put_contents($newsletterFile, json_encode($subscriberData) . "\n", FILE_APPEND | LOCK_EX);
    

    // Send notification email to company using PHPMailer
    $emailSubject = 'New Newsletter Subscription - ' . SITE_NAME;
    $emailContent = generateNewsletterNotificationEmail($email);
    $adminMail = new PHPMailer(true);
    $adminSent = false;
    try {
        $adminMail->isSMTP();
        $adminMail->Host = SMTP_HOST;
        $adminMail->SMTPAuth = true;
        $adminMail->Username = SMTP_USERNAME;
        $adminMail->Password = SMTP_PASSWORD;
        $adminMail->SMTPSecure = SMTP_SECURE;
        $adminMail->Port = SMTP_PORT;
        $adminMail->setFrom(SITE_EMAIL, SITE_NAME);
        $adminMail->addAddress(SITE_EMAIL);
        $adminMail->addReplyTo($email);
        $adminMail->isHTML(true);
        $adminMail->Subject = $emailSubject;
        $adminMail->Body = $emailContent;
        $adminMail->send();
        $adminSent = true;
    } catch (Exception $e) {
        error_log('Newsletter admin PHPMailer error: ' . $adminMail->ErrorInfo);
    }

    // Send welcome email to subscriber using PHPMailer
    $welcomeSubject = 'Welcome to ' . SITE_NAME . ' Newsletter!';
    $welcomeContent = generateWelcomeEmail($email);
    $userMail = new PHPMailer(true);
    $userSent = false;
    try {
        $userMail->isSMTP();
        $userMail->Host = SMTP_HOST;
        $userMail->SMTPAuth = true;
        $userMail->Username = SMTP_USERNAME;
        $userMail->Password = SMTP_PASSWORD;
        $userMail->SMTPSecure = SMTP_SECURE;
        $userMail->Port = SMTP_PORT;
        $userMail->setFrom(SITE_EMAIL, SITE_NAME);
        $userMail->addAddress($email);
        $userMail->isHTML(true);
        $userMail->Subject = $welcomeSubject;
        $userMail->Body = $welcomeContent;
        $userMail->send();
        $userSent = true;
    } catch (Exception $e) {
        error_log('Newsletter welcome PHPMailer error: ' . $userMail->ErrorInfo);
    }

    if ($adminSent && $userSent) {
        echo json_encode([
            'success' => true,
            'message' => 'Thank you for subscribing! You will receive our latest logistics updates and exclusive offers.'
        ]);
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Subscription saved, but there was an error sending confirmation emails.'
        ]);
    }
    
} catch (Exception $e) {
    error_log('Newsletter subscription error: ' . $e->getMessage());
    echo json_encode([
        'success' => false, 
        'message' => 'An unexpected error occurred. Please try again later.'
    ]);
}

function generateNewsletterNotificationEmail($email) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>New Newsletter Subscription</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1E3A8A, #FF7A3D); color: white; padding: 20px; text-align: center; }
            .content { background: #f9f9f9; padding: 20px; }
            .footer { background: #1E3A8A; color: white; padding: 15px; text-align: center; font-size: 12px; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>' . SITE_NAME . '</h1>
                <h2>New Newsletter Subscription</h2>
            </div>
            <div class="content">
                <p><strong>A new user has subscribed to your newsletter:</strong></p>
                <p><strong>Email:</strong> ' . $email . '</p>
                <p><strong>Date:</strong> ' . date('Y-m-d H:i:s') . '</p>
                <p><strong>IP Address:</strong> ' . ($_SERVER['REMOTE_ADDR'] ?? 'unknown') . '</p>
            </div>
            <div class="footer">
                <p>' . SITE_EMAIL . ' | ' . COMPANY_PHONE . '</p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}

function generateWelcomeEmail($email) {
    $html = '
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <title>Welcome to ' . SITE_NAME . ' Newsletter</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background: linear-gradient(135deg, #1E3A8A, #FF7A3D); color: white; padding: 30px; text-align: center; }
            .content { background: #f9f9f9; padding: 30px; }
            .highlight { background: #FF7A3D; color: white; padding: 15px; border-radius: 5px; margin: 20px 0; }
            .footer { background: #1E3A8A; color: white; padding: 20px; text-align: center; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>Welcome to ' . SITE_NAME . '!</h1>
                <p>Thank you for subscribing to our newsletter</p>
            </div>
            <div class="content">
                <h2>Hello and Welcome!</h2>
                <p>Thank you for subscribing to the ' . SITE_NAME . ' newsletter. You\'ve just joined a community of logistics professionals and businesses who stay ahead of industry trends.</p>
                
                <div class="highlight">
                    <h3>What to Expect:</h3>
                    <ul>
                        <li>Latest logistics and shipping industry news</li>
                        <li>Exclusive offers and competitive pricing updates</li>
                        <li>Expert tips for optimizing your supply chain</li>
                        <li>Market insights and commodity trading updates</li>
                        <li>Company news and service announcements</li>
                    </ul>
                </div>
                
                <p>Our newsletter is sent monthly, and we promise to keep your information secure and never share it with third parties.</p>
                
                <p>If you have any questions or need immediate assistance with your logistics needs, don\'t hesitate to contact us:</p>
                <ul>
                    <li><strong>Email:</strong> ' . SITE_EMAIL . '</li>
                    <li><strong>Phone:</strong> ' . COMPANY_PHONE . '</li>
                    <li><strong>Address:</strong> ' . COMPANY_ADDRESS . '</li>
                </ul>
                
                <p>Thank you for choosing ' . SITE_NAME . ' as your trusted logistics partner!</p>
            </div>
            <div class="footer">
                <p><strong>' . SITE_NAME . '</strong></p>
                <p>' . SITE_EMAIL . ' | ' . COMPANY_PHONE . '</p>
                <p>' . COMPANY_ADDRESS . '</p>
                <p style="font-size: 10px; margin-top: 15px;">
                    If you no longer wish to receive these emails, please contact us to unsubscribe.
                </p>
            </div>
        </div>
    </body>
    </html>';
    
    return $html;
}
?>
