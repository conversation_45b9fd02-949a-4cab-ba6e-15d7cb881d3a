<?php
// PROMTECH EXPORT & IMPORT - Configuration File
// Website configuration and constants

// Dynamic Base URL Detection
function getBaseUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') ? 'https://' : 'http://';
    $host = $_SERVER['HTTP_HOST'];
    $script = $_SERVER['SCRIPT_NAME'];
    $path = dirname($script);

    // Remove trailing slash if not root
    if ($path !== '/') {
        $path = rtrim($path, '/');
    }

    return $protocol . $host . $path;
}

// Dynamic Asset Path Function
function getAssetUrl($path) {
    $baseUrl = getBaseUrl();
    return $baseUrl . '/' . ltrim($path, '/');
}

// Site Information
define('SITE_NAME', 'PROMTECH EXPORT & IMPORT');
define('SITE_TAGLINE', 'Global Trade Solutions');
define('SITE_URL', getBaseUrl()); // Dynamic base URL
define('SITE_EMAIL', '<EMAIL>');
define('CEO_EMAIL', '<EMAIL>');

// Company Information
define('COMPANY_NAME', 'PROMTECH EXPORT & IMPORT');
define('COMPANY_ADDRESS', '74 Hrushevsky Street, Kiev, 01008, Ukraine');
define('COMPANY_PHONE', '+380 44 555 0123');
define('CEO_NAME', 'Mr. Petrov Denysenko');

// SMTP Settings for PHPMailer
define('SMTP_HOST', 'smtp.hostinger.com');
define('SMTP_PORT', 465); // or 587 for TLS
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 't!S5jTsg');
define('SMTP_SECURE', 'ssl'); // or 'tls'

// Navigation Menu Structure
$navigation_menu = [
    'home' => [
        'title' => '<i class="ri-home-line"></i>',
        'url' => 'index.php',
        'active' => false
    ],
    'about' => [
        'title' => 'About Us', 
        'url' => 'about.php',
        'active' => false
    ],
    'commodities' => [
        'title' => 'Commodities',
        'url' => 'commodities.php', 
        'active' => false
    ],
    'markets' => [
        'title' => 'Markets',
        'url' => '#',
        'active' => false,
        'submenu' => [
            'grain' => [
                'title' => 'Grain Market',
                'url' => 'grain-market.php'
            ],
            'wood' => [
                'title' => 'Wood Market', 
                'url' => 'wood-market.php'
            ]
        ]
    ],
    'services' => [
        'title' => 'Services',
        'url' => 'services.php',
        'active' => false,
        'submenu' => [
            'ocean-freight' => [
                'title' => 'Ocean Freight',
                'url' => 'ocean-freight.php'
            ],
            'air-freight' => [
                'title' => 'Air Freight',
                'url' => 'air-freight.php'
            ],
            'road-freight' => [
                'title' => 'Road Freight',
                'url' => 'road-freight.php'
            ],
            'warehousing' => [
                'title' => 'Warehousing',
                'url' => 'warehousing.php'
            ],
            'custom-clearance' => [
                'title' => 'Custom Clearance',
                'url' => 'custom-clearance.php'
            ],
            'shipping-calculator' => [
                'title' => 'Shipping Calculator',
                'url' => 'shipping-calculator.php'
            ],
            'service-comparison' => [
                'title' => 'Service Comparison',
                'url' => 'service-comparison.php'
            ]
        ]
    ],
    'industries' => [
        'title' => 'Industries',
        'url' => '#',
        'active' => false,
        'submenu' => [
            'manufacturing' => [
                'title' => 'Manufacturing',
                'url' => 'industry-manufacturing.php'
            ],
            'agriculture' => [
                'title' => 'Agriculture',
                'url' => 'industry-agriculture.php'
            ],
            'retail' => [
                'title' => 'Retail',
                'url' => 'industry-retail.php'
            ]
        ]
    ],
    'resources' => [
        'title' => 'Resources',
        'url' => '#',
        'active' => false,
        'submenu' => [
            'case-studies' => [
                'title' => 'Case Studies',
                'url' => 'case-studies.php'
            ],
            'blog' => [
                'title' => 'Blog',
                'url' => 'blog.php'
            ],
            'faq' => [
                'title' => 'FAQ',
                'url' => 'faq.php'
            ]
        ]
    ],
    'contact' => [
        'title' => 'Contact Us',
        'url' => 'contact.php',
        'active' => false
    ]
];

// Set active page based on current file
function setActivePage($current_page) {
    global $navigation_menu;

    foreach ($navigation_menu as $key => &$item) {
        $item['active'] = false;

        // Check main menu items
        if (basename($_SERVER['PHP_SELF']) === $item['url']) {
            $item['active'] = true;
        }

        // Check submenu items
        if (isset($item['submenu'])) {
            foreach ($item['submenu'] as $sub_key => &$sub_item) {
                if (basename($_SERVER['PHP_SELF']) === $sub_item['url']) {
                    $item['active'] = true;
                    $sub_item['active'] = true;
                }
            }
        }
    }
}

// Breadcrumb Navigation System
function generateBreadcrumbs($current_page = '') {
    global $navigation_menu;

    if (empty($current_page)) {
        $current_page = basename($_SERVER['PHP_SELF']);
    }

    $breadcrumbs = [
        [
            'title' => 'Home',
            'url' => 'index.php',
            'icon' => 'ri-home-line'
        ]
    ];

    // Define page hierarchy and relationships
    $page_hierarchy = [
        // Services pages
        'services.php' => [
            'parent' => null,
            'title' => 'Services',
            'icon' => 'ri-service-line'
        ],
        'ocean-freight.php' => [
            'parent' => 'services.php',
            'title' => 'Ocean Freight',
            'icon' => 'ri-ship-line'
        ],
        'air-freight.php' => [
            'parent' => 'services.php',
            'title' => 'Air Freight',
            'icon' => 'ri-plane-line'
        ],
        'road-freight.php' => [
            'parent' => 'services.php',
            'title' => 'Road Freight',
            'icon' => 'ri-truck-line'
        ],
        'warehousing.php' => [
            'parent' => 'services.php',
            'title' => 'Warehousing',
            'icon' => 'ri-building-line'
        ],
        'custom-clearance.php' => [
            'parent' => 'services.php',
            'title' => 'Custom Clearance',
            'icon' => 'ri-file-list-line'
        ],

        // Market pages
        'grain-market.php' => [
            'parent' => 'commodities.php',
            'title' => 'Grain Market',
            'icon' => 'ri-plant-line'
        ],
        'wood-market.php' => [
            'parent' => 'commodities.php',
            'title' => 'Wood Market',
            'icon' => 'ri-tree-line'
        ],

        // Commodity pages
        'commodities.php' => [
            'parent' => null,
            'title' => 'Commodities',
            'icon' => 'ri-box-line'
        ],

        // Other pages
        'about.php' => [
            'parent' => null,
            'title' => 'About Us',
            'icon' => 'ri-team-line'
        ],
        'contact.php' => [
            'parent' => null,
            'title' => 'Contact Us',
            'icon' => 'ri-mail-line'
        ],
        'quote.php' => [
            'parent' => null,
            'title' => 'Get Quote',
            'icon' => 'ri-calculator-line'
        ]
    ];

    // Build breadcrumb trail
    if (isset($page_hierarchy[$current_page])) {
        $page_info = $page_hierarchy[$current_page];
        $trail = [];

        // Build trail from current page back to root
        $current = $current_page;
        while ($current && isset($page_hierarchy[$current])) {
            $info = $page_hierarchy[$current];
            array_unshift($trail, [
                'title' => $info['title'],
                'url' => $current,
                'icon' => $info['icon']
            ]);
            $current = $info['parent'];
        }

        // Add parent pages to breadcrumbs
        foreach ($trail as $item) {
            if ($item['url'] !== $current_page) {
                $breadcrumbs[] = $item;
            }
        }

        // Add current page (without link)
        $current_info = $page_hierarchy[$current_page];
        $breadcrumbs[] = [
            'title' => $current_info['title'],
            'url' => null, // No link for current page
            'icon' => $current_info['icon'],
            'current' => true
        ];
    }

    return $breadcrumbs;
}

// Render breadcrumb HTML
function renderBreadcrumbs($current_page = '') {
    $breadcrumbs = generateBreadcrumbs($current_page);

    if (count($breadcrumbs) <= 1) {
        return ''; // Don't show breadcrumbs on home page
    }

    $html = '<nav class="breadcrumb-nav bg-gray-50 py-3 border-b" aria-label="Breadcrumb">';
    $html .= '<div class="container mx-auto px-4">';
    $html .= '<ol class="flex items-center space-x-2 text-sm">';

    foreach ($breadcrumbs as $index => $crumb) {
        $html .= '<li class="flex items-center">';

        if ($index > 0) {
            $html .= '<i class="ri-arrow-right-s-line text-gray-400 mx-2"></i>';
        }

        if (isset($crumb['current']) && $crumb['current']) {
            // Current page - no link
            $html .= '<span class="flex items-center text-primary font-medium">';
            if (isset($crumb['icon'])) {
                $html .= '<i class="' . $crumb['icon'] . ' mr-1"></i>';
            }
            $html .= $crumb['title'];
            $html .= '</span>';
        } else {
            // Linked breadcrumb
            $html .= '<a href="' . $crumb['url'] . '" class="flex items-center text-gray-600 hover:text-primary transition-colors">';
            if (isset($crumb['icon'])) {
                $html .= '<i class="' . $crumb['icon'] . ' mr-1"></i>';
            }
            $html .= $crumb['title'];
            $html .= '</a>';
        }

        $html .= '</li>';
    }

    $html .= '</ol>';
    $html .= '</div>';
    $html .= '</nav>';

    return $html;
}

// Search Functionality System
function getSearchableContent() {
    return [
        // Services
        [
            'title' => 'Ocean Freight Services',
            'url' => 'ocean-freight.php',
            'category' => 'Services',
            'keywords' => ['ocean', 'freight', 'sea', 'cargo', 'shipping', 'maritime', 'container', 'FCL', 'LCL', 'international'],
            'description' => 'Professional ocean freight services for international sea cargo transportation with reliable shipping schedules and competitive rates.',
            'icon' => 'ri-ship-line'
        ],
        [
            'title' => 'Air Freight Services',
            'url' => 'air-freight.php',
            'category' => 'Services',
            'keywords' => ['air', 'freight', 'cargo', 'aviation', 'express', 'fast', 'urgent', 'international', 'delivery'],
            'description' => 'Fast and secure air cargo services for time-sensitive shipments with global network coverage and express delivery options.',
            'icon' => 'ri-plane-line'
        ],
        [
            'title' => 'Road Freight Services',
            'url' => 'road-freight.php',
            'category' => 'Services',
            'keywords' => ['road', 'freight', 'truck', 'overland', 'ground', 'transportation', 'delivery', 'logistics'],
            'description' => 'Efficient ground transportation services with flexible routing options and door-to-door delivery across multiple regions.',
            'icon' => 'ri-truck-line'
        ],
        [
            'title' => 'Warehousing Services',
            'url' => 'warehousing.php',
            'category' => 'Services',
            'keywords' => ['warehouse', 'storage', 'distribution', 'inventory', 'fulfillment', 'logistics'],
            'description' => 'Comprehensive warehousing and distribution solutions with modern facilities and inventory management systems.',
            'icon' => 'ri-building-line'
        ],
        [
            'title' => 'Custom Clearance Services',
            'url' => 'custom-clearance.php',
            'category' => 'Services',
            'keywords' => ['customs', 'clearance', 'documentation', 'import', 'export', 'compliance', 'regulations'],
            'description' => 'Expert customs clearance services ensuring smooth import/export operations with full regulatory compliance.',
            'icon' => 'ri-file-list-line'
        ],

        // Markets & Commodities
        [
            'title' => 'Grain Market',
            'url' => 'grain-market.php',
            'category' => 'Markets',
            'keywords' => ['grain', 'wheat', 'corn', 'barley', 'agricultural', 'farming', 'cereals', 'food'],
            'description' => 'High-quality wheat, corn, barley, and other grain commodities sourced from premium agricultural regions.',
            'icon' => 'ri-plant-line'
        ],
        [
            'title' => 'Wood Market',
            'url' => 'wood-market.php',
            'category' => 'Markets',
            'keywords' => ['wood', 'timber', 'lumber', 'forestry', 'sustainable', 'construction', 'materials'],
            'description' => 'Premium timber, lumber, and processed wood materials from sustainable forestry operations worldwide.',
            'icon' => 'ri-tree-line'
        ],

        // Main Pages
        [
            'title' => 'All Services',
            'url' => 'services.php',
            'category' => 'Services',
            'keywords' => ['services', 'logistics', 'transportation', 'freight', 'shipping', 'cargo'],
            'description' => 'Comprehensive logistics and transportation services for all your import/export needs.',
            'icon' => 'ri-service-line'
        ],
        [
            'title' => 'All Commodities',
            'url' => 'commodities.php',
            'category' => 'Commodities',
            'keywords' => ['commodities', 'products', 'trading', 'materials', 'goods', 'merchandise'],
            'description' => 'Wide range of high-quality commodities and products for international trade.',
            'icon' => 'ri-box-line'
        ],
        [
            'title' => 'About Us',
            'url' => 'about.php',
            'category' => 'Company',
            'keywords' => ['about', 'company', 'team', 'history', 'mission', 'vision', 'leadership'],
            'description' => 'Learn about PROMTECH Export & Import - our company profile, leadership team, and business mission.',
            'icon' => 'ri-team-line'
        ],
        [
            'title' => 'Contact Us',
            'url' => 'contact.php',
            'category' => 'Contact',
            'keywords' => ['contact', 'support', 'help', 'phone', 'email', 'address', 'location'],
            'description' => 'Get in touch with our team for inquiries, support, or business partnerships.',
            'icon' => 'ri-mail-line'
        ],
        [
            'title' => 'Get Quote',
            'url' => 'quote.php',
            'category' => 'Services',
            'keywords' => ['quote', 'estimate', 'pricing', 'cost', 'request', 'calculation'],
            'description' => 'Request a detailed quote for your logistics and transportation needs.',
            'icon' => 'ri-calculator-line'
        ]
    ];
}

function performSearch($query, $category = '', $limit = 10) {
    $searchableContent = getSearchableContent();
    $results = [];
    $query = strtolower(trim($query));

    if (empty($query)) {
        return $results;
    }

    foreach ($searchableContent as $item) {
        $score = 0;

        // Filter by category if specified
        if (!empty($category) && strtolower($item['category']) !== strtolower($category)) {
            continue;
        }

        // Title match (highest priority)
        if (stripos($item['title'], $query) !== false) {
            $score += 100;
            if (stripos(strtolower($item['title']), $query) === 0) {
                $score += 50; // Bonus for starting with query
            }
        }

        // Keywords match
        foreach ($item['keywords'] as $keyword) {
            if (stripos($keyword, $query) !== false) {
                $score += 20;
                if (strtolower($keyword) === $query) {
                    $score += 30; // Exact keyword match
                }
            }
        }

        // Description match
        if (stripos($item['description'], $query) !== false) {
            $score += 10;
        }

        // Category match
        if (stripos($item['category'], $query) !== false) {
            $score += 15;
        }

        if ($score > 0) {
            $item['score'] = $score;
            $item['highlighted_title'] = highlightSearchTerm($item['title'], $query);
            $item['highlighted_description'] = highlightSearchTerm($item['description'], $query);
            $results[] = $item;
        }
    }

    // Sort by score (highest first)
    usort($results, function($a, $b) {
        return $b['score'] - $a['score'];
    });

    return array_slice($results, 0, $limit);
}

function highlightSearchTerm($text, $term) {
    return preg_replace('/(' . preg_quote($term, '/') . ')/i', '<mark class="bg-yellow-200 px-1 rounded">$1</mark>', $text);
}

function getSearchSuggestions($query, $limit = 5) {
    $searchableContent = getSearchableContent();
    $suggestions = [];
    $query = strtolower(trim($query));

    if (strlen($query) < 2) {
        return $suggestions;
    }

    foreach ($searchableContent as $item) {
        // Check title
        if (stripos($item['title'], $query) !== false) {
            $suggestions[] = $item['title'];
        }

        // Check keywords
        foreach ($item['keywords'] as $keyword) {
            if (stripos($keyword, $query) !== false && !in_array($keyword, $suggestions)) {
                $suggestions[] = ucfirst($keyword);
            }
        }
    }

    return array_slice(array_unique($suggestions), 0, $limit);
}

// Shipping Calculator Functions
function getShippingServices() {
    return [
        'ocean_fcl' => [
            'name' => 'Ocean Freight - FCL',
            'type' => 'ocean',
            'subtype' => 'fcl',
            'description' => 'Full Container Load - Dedicated container for your cargo',
            'base_rate' => 1200, // USD per container
            'transit_time' => '15-25 days',
            'min_weight' => 1000, // kg
            'container_types' => ['20ft', '40ft', '40ft HC'],
            'features' => ['Dedicated container', 'Door-to-door', 'Cargo insurance', 'Real-time tracking']
        ],
        'ocean_lcl' => [
            'name' => 'Ocean Freight - LCL',
            'type' => 'ocean',
            'subtype' => 'lcl',
            'description' => 'Less than Container Load - Share container space',
            'base_rate' => 45, // USD per CBM
            'transit_time' => '18-30 days',
            'min_weight' => 100, // kg
            'min_volume' => 1, // CBM
            'features' => ['Cost-effective', 'Weekly sailings', 'Port-to-port', 'Consolidated cargo']
        ],
        'air_express' => [
            'name' => 'Air Freight - Express',
            'type' => 'air',
            'subtype' => 'express',
            'description' => 'Fastest delivery for urgent shipments',
            'base_rate' => 8.5, // USD per kg
            'transit_time' => '1-3 days',
            'min_weight' => 1, // kg
            'features' => ['24-48 hour delivery', 'Priority handling', 'Real-time tracking', 'Door-to-door']
        ],
        'air_standard' => [
            'name' => 'Air Freight - Standard',
            'type' => 'air',
            'subtype' => 'standard',
            'description' => 'Cost-effective air cargo solution',
            'base_rate' => 4.2, // USD per kg
            'transit_time' => '3-7 days',
            'min_weight' => 1, // kg
            'features' => ['Competitive rates', 'Regular schedules', 'Cargo insurance', 'Professional handling']
        ],
        'road_freight' => [
            'name' => 'Road Freight',
            'type' => 'road',
            'subtype' => 'standard',
            'description' => 'Overland transportation across Europe and Asia',
            'base_rate' => 1.8, // USD per kg
            'transit_time' => '5-12 days',
            'min_weight' => 50, // kg
            'features' => ['Door-to-door', 'Flexible scheduling', 'Temperature control', 'Partial loads']
        ]
    ];
}

function getShippingRoutes() {
    return [
        'Ukraine' => [
            'Europe' => ['distance' => 1200, 'zone' => 'A', 'multiplier' => 1.0],
            'Asia' => ['distance' => 4500, 'zone' => 'B', 'multiplier' => 1.3],
            'North America' => ['distance' => 8500, 'zone' => 'C', 'multiplier' => 1.6],
            'South America' => ['distance' => 12000, 'zone' => 'D', 'multiplier' => 1.8],
            'Africa' => ['distance' => 6500, 'zone' => 'B', 'multiplier' => 1.4],
            'Oceania' => ['distance' => 15000, 'zone' => 'D', 'multiplier' => 2.0]
        ]
    ];
}

function calculateShippingCost($service_type, $origin, $destination, $weight, $dimensions = null, $cargo_type = 'general') {
    $services = getShippingServices();
    $routes = getShippingRoutes();

    if (!isset($services[$service_type])) {
        return ['error' => 'Invalid service type'];
    }

    $service = $services[$service_type];
    $route_info = $routes[$origin][$destination] ?? ['multiplier' => 1.5, 'zone' => 'C'];

    // Calculate base cost
    $base_cost = 0;

    if ($service['type'] === 'ocean') {
        if ($service['subtype'] === 'fcl') {
            // FCL pricing per container
            $base_cost = $service['base_rate'] * $route_info['multiplier'];
        } else {
            // LCL pricing per CBM
            $volume = calculateVolume($dimensions) ?: ($weight / 300); // Default density
            $base_cost = $service['base_rate'] * $volume * $route_info['multiplier'];
        }
    } else {
        // Air and Road freight per kg
        $base_cost = $service['base_rate'] * $weight * $route_info['multiplier'];
    }

    // Apply cargo type multiplier
    $cargo_multipliers = [
        'general' => 1.0,
        'hazardous' => 1.5,
        'perishable' => 1.3,
        'fragile' => 1.2,
        'oversized' => 1.4
    ];

    $cargo_multiplier = $cargo_multipliers[$cargo_type] ?? 1.0;
    $base_cost *= $cargo_multiplier;

    // Add additional fees
    $additional_fees = [
        'fuel_surcharge' => $base_cost * 0.15,
        'security_fee' => min($base_cost * 0.05, 200),
        'documentation' => 75,
        'handling' => $weight * 0.5
    ];

    $total_additional = array_sum($additional_fees);
    $subtotal = $base_cost + $total_additional;
    $total_cost = $subtotal;

    return [
        'service' => $service,
        'base_cost' => round($base_cost, 2),
        'additional_fees' => $additional_fees,
        'subtotal' => round($subtotal, 2),
        'total_cost' => round($total_cost, 2),
        'currency' => 'USD',
        'transit_time' => $service['transit_time'],
        'route_info' => $route_info
    ];
}

function calculateVolume($dimensions) {
    if (!$dimensions || !isset($dimensions['length'], $dimensions['width'], $dimensions['height'])) {
        return null;
    }

    // Convert to meters and calculate CBM
    $length = $dimensions['length'] / 100; // cm to m
    $width = $dimensions['width'] / 100;
    $height = $dimensions['height'] / 100;

    return $length * $width * $height;
}

function compareShippingServices($origin, $destination, $weight, $dimensions = null, $cargo_type = 'general') {
    $services = array_keys(getShippingServices());
    $comparisons = [];

    foreach ($services as $service_type) {
        $calculation = calculateShippingCost($service_type, $origin, $destination, $weight, $dimensions, $cargo_type);
        if (!isset($calculation['error'])) {
            $comparisons[$service_type] = $calculation;
        }
    }

    // Sort by total cost
    uasort($comparisons, function($a, $b) {
        return $a['total_cost'] <=> $b['total_cost'];
    });

    return $comparisons;
}

// Helper function to get page title
function getPageTitle($page = '') {
    $titles = [
        'index.php' => 'Home - Leading Platform for Global Trade Solutions',
        'about.php' => 'About Us - Company Profile & Leadership',
        'commodities.php' => 'Our Commodities - Agricultural, Chemical, Mineral Products',
        'grain-market.php' => 'Grain Market - Ukraine Grain Exports',
        'wood-market.php' => 'Wood Market - Timber & Sustainable Sourcing',
        'services.php' => 'Our Services - End-to-End Trade Operations',
        'ocean-freight.php' => 'Ocean Freight - International Sea Cargo Services',
        'air-freight.php' => 'Air Freight - Fast International Air Cargo',
        'road-freight.php' => 'Road Freight - Overland Transportation Services',
        'warehousing.php' => 'Warehousing - Storage & Distribution Solutions',
        'custom-clearance.php' => 'Custom Clearance - Import Export Documentation',
        'contact.php' => 'Contact Us - Get in Touch',
        'privacy-policy.php' => 'Privacy Policy - Data Protection & Privacy',
        'terms-of-service.php' => 'Terms of Service - Service Terms & Conditions',
        'disclaimer.php' => 'Disclaimer - Important Service Information'
    ];
    
    $current_page = basename($_SERVER['PHP_SELF']);
    return isset($titles[$current_page]) ? $titles[$current_page] : SITE_NAME . ' - ' . SITE_TAGLINE;
}

// Helper function to get page description
function getPageDescription($page = '') {
    $descriptions = [
        'index.php' => 'PROMTECH EXPORT & IMPORT - Leading Ukrainian logistics company specialising in global trade solutions, import-export operations, and supply chain management.',
        'about.php' => 'Learn about PROMTECH leadership, company history, mission & vision. Meet CEO Mr. Petrov Denysenko and discover our global network.',
        'commodities.php' => 'Explore our wide range of commodities: agricultural products, chemical goods, mineral products, wood & timber, metal scrap, machinery and tools.',
        'grain-market.php' => 'Ukraine grain market overview, export trends, and available grain products from PROMTECH EXPORT & IMPORT.',
        'wood-market.php' => 'Sustainable timber sourcing, wood market analysis, and certified wood products from Ukrainian forests.',
        'services.php' => 'Comprehensive trade services: export/import documentation, customs clearance, freight handling, logistics & warehousing.',
        'ocean-freight.php' => 'Professional ocean freight services for international sea cargo. FCL, LCL shipping solutions from Ukraine to worldwide destinations.',
        'air-freight.php' => 'Fast and reliable air freight services for urgent international cargo. Express air shipping solutions worldwide.',
        'road-freight.php' => 'Efficient road freight and overland transportation services across Europe and Asia. Truck cargo solutions.',
        'warehousing.php' => 'Modern warehousing and distribution solutions. Storage facilities, inventory management, and logistics services.',
        'custom-clearance.php' => 'Expert customs clearance services for import and export. Documentation, duties calculation, and compliance support.',
        'contact.php' => 'Contact PROMTECH EXPORT & IMPORT. Get in touch with our team in Kiev, Ukraine for global trade solutions.',
        'privacy-policy.php' => 'Privacy Policy for PROMTECH EXPORT & IMPORT. Learn how we collect, use, and protect your personal information.',
        'terms-of-service.php' => 'Terms of Service for PROMTECH EXPORT & IMPORT. Read our terms and conditions for using our services.',
        'disclaimer.php' => 'Disclaimer for PROMTECH EXPORT & IMPORT. Important information about our services and limitations.'
    ];
    
    $current_page = basename($_SERVER['PHP_SELF']);
    return isset($descriptions[$current_page]) ? $descriptions[$current_page] : 'PROMTECH EXPORT & IMPORT - Global Trade Solutions';
}

// Initialize active page
setActivePage(basename($_SERVER['PHP_SELF']));
?>
