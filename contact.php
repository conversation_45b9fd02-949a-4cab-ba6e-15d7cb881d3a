<?php 
require_once 'config.php';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Contact Us - <?php echo COMPANY_NAME; ?></title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colours: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .hero-banner {
            background: linear-gradient(rgba(30, 58, 138, 0.6), rgba(255, 122, 61, 0.4)), url('<?php echo getAssetUrl('assets/img/img6.webp'); ?>');
            background-size: cover;
            background-position: centre centre;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        .rounded-button {
            border-radius: 25px;
        }
        .form-input {
            transition: all 0.3s ease;
        }
        .form-input:focus {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(255, 122, 61, 0.2);
        }
    </style>
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>

    <!-- Hero Banner Section -->
    <section class="hero-banner min-h-[400px] flex items-center justify-center relative">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary/80 to-primary/60"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <nav class="flex items-center justify-center space-x-2 mb-6">
                    <a href="index.php" class="text-white/80 hover:text-white transition-colors">
                        <i class="ri-home-line mr-1"></i>Home
                    </a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <span class="text-white font-medium">Contact us</span>
                </nav>
                <h1 class="text-5xl md:text-6xl font-bold mb-4">Contact us</h1>
                <p class="text-xl text-white/90 max-w-2xl mx-auto">
                    Get in touch with our logistics experts for personalized solutions and professional consultation.
                </p>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-6xl mx-auto">
                <div class="grid lg:grid-cols-2 gap-12 items-start">
                    <!-- Left Column - Contact Info -->
                    <div>
                        <div class="mb-8">
                            <span class="text-primary font-semibold text-lg">Contact</span>
                            <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">Get in touch with us</h2>
                            <p class="text-gray-600 text-lg leading-relaxed">
                                Ready to streamline your logistics operations? Contact our expert team for personalized solutions 
                                that meet your specific shipping and trading needs. We're here to help you succeed.
                            </p>
                        </div>

                        <div class="space-y-6 mb-8">
                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-map-pin-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Our Address</h3>
                                    <p class="text-gray-600">74 Hrushevsky Street<br>Kiev, 01008, Ukraine</p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-phone-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Phone Number</h3>
                                    <p class="text-gray-600"><?php echo COMPANY_PHONE; ?></p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-mail-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Email Address</h3>
                                    <p class="text-gray-600"><?php echo SITE_EMAIL; ?></p>
                                </div>
                            </div>

                            <div class="flex items-start space-x-4">
                                <div class="bg-primary/10 p-3 rounded-lg">
                                    <i class="ri-time-line text-primary text-xl"></i>
                                </div>
                                <div>
                                    <h3 class="font-semibold text-secondary mb-2">Business Hours</h3>
                                    <p class="text-gray-600">
                                        Mon - Fri: 9:00 AM - 6:00 PM<br>
                                        Saturday: 9:00 AM - 4:00 PM<br>
                                        Sunday: Closed
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Contact Options -->
                        <div class="bg-gray-50 p-6 rounded-2xl">
                            <h3 class="font-semibold text-secondary mb-4">Quick Contact</h3>
                            <div class="flex flex-col sm:flex-row gap-4">
                                <a href="tel:<?php echo str_replace(' ', '', COMPANY_PHONE); ?>" 
                                   class="flex items-center justify-center bg-primary text-white px-6 py-3 rounded-button font-semibold hover:bg-primary/90 transition-colors">
                                    <i class="ri-phone-line mr-2"></i>Call Now
                                </a>
                                <a href="mailto:<?php echo SITE_EMAIL; ?>"
                                   class="flex items-center justify-center bg-secondary text-white px-6 py-3 rounded-button font-semibold hover:bg-secondary/90 transition-colors">
                                    <i class="ri-mail-line mr-2"></i>Send Email
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Right Column - Contact Form -->
                    <div class="bg-gray-50 p-8 rounded-2xl shadow-lg">
                        <h3 class="text-2xl font-bold text-secondary mb-6">Active & Ready to use Contact Form!</h3>
                        <form id="contactForm" class="space-y-6">
                            <div class="grid md:grid-cols-2 gap-4">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">First Name *</label>
                                    <input type="text" name="firstName" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">Last Name *</label>
                                    <input type="text" name="lastName" required 
                                           class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                </div>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Your Email *</label>
                                <input type="email" name="email" required 
                                       class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Phone Number *</label>
                                <input type="tel" name="phone" required 
                                       class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Company Name</label>
                                <input type="text" name="company" 
                                       class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Subject *</label>
                                <select name="subject" required 
                                        class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                                    <option value="">Select Subject</option>
                                    <option value="General Inquiry">General Inquiry</option>
                                    <option value="Quote Request">Quote Request</option>
                                    <option value="Customer Support">Customer Support</option>
                                    <option value="Partnership Opportunity">Partnership Opportunity</option>
                                    <option value="Complaint">Complaint</option>
                                    <option value="Feedback">Feedback</option>
                                    <option value="Other">Other</option>
                                </select>
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Message *</label>
                                <textarea name="message" rows="5" required placeholder="Tell us about your logistics needs, questions, or how we can help you..."
                                          class="form-input w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent resize-none"></textarea>
                            </div>

                            <div class="flex items-start space-x-3">
                                <input type="checkbox" id="privacy" name="privacy" required 
                                       class="mt-1 w-4 h-4 text-primary border-gray-300 rounded focus:ring-primary">
                                <label for="privacy" class="text-sm text-gray-600">
                                    I agree to the <a href="privacy-policy.php" class="text-primary hover:underline">Privacy Policy</a> and
                                    <a href="terms-of-service.php" class="text-primary hover:underline">Terms of Service</a> *
                                </label>
                            </div>

                            <button type="submit" 
                                    class="w-full bg-primary text-white py-4 px-8 rounded-button font-semibold text-lg hover:bg-primary/90 transition-colors flex items-center justify-center">
                                <i class="ri-send-plane-line mr-2"></i>
                                Send Message
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Office Hours & Services Section -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <span class="text-primary font-semibold text-lg">Our Services</span>
                <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">How We Can Help You</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Discover our comprehensive range of logistics services designed to meet your business needs.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-ship-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Ocean Freight</h3>
                    <p class="text-gray-600">Global sea freight solutions with competitive rates</p>
                </div>
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-plane-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Air Freight</h3>
                    <p class="text-gray-600">Fast and secure air cargo services worldwide</p>
                </div>
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-truck-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Road Freight</h3>
                    <p class="text-gray-600">Efficient ground transportation across regions</p>
                </div>
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-building-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Warehousing</h3>
                    <p class="text-gray-600">Secure storage and distribution facilities</p>
                </div>
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-file-text-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Custom Clearance</h3>
                    <p class="text-gray-600">Expert customs documentation and clearance</p>
                </div>
                <div class="bg-white p-6 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-4">
                        <i class="ri-exchange-line text-primary text-2xl"></i>
                    </div>
                    <h3 class="font-semibold text-secondary mb-2">Commodity Trading</h3>
                    <p class="text-gray-600">Quality grain, wood, metal, and agricultural products</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Map Section -->
    <section class="py-16 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-12">
                <h2 class="text-4xl font-bold text-secondary mb-4">Visit Our Office</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Located in the heart of Kiev, our office is easily accessible for face-to-face consultations and meetings.
                </p>
            </div>
            <div class="bg-gradient-to-br from-gray-100 to-gray-200 h-96 rounded-2xl flex items-center justify-center relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-r from-secondary/10 to-primary/10"></div>
                <div class="text-center relative z-10">
                    <div class="bg-white/90 backdrop-blur-sm p-8 rounded-2xl shadow-lg">
                        <i class="ri-map-pin-line text-6xl text-primary mb-4"></i>
                        <h3 class="text-2xl font-bold text-secondary mb-2">PROMTECH Office</h3>
                        <p class="text-gray-600 mb-4">74 Hrushevsky Street<br>Kiev, 01008, Ukraine</p>
                        <a href="https://maps.google.com/?q=74+Hrushevsky+Street,+Kiev,+Ukraine" target="_blank"
                           class="inline-flex items-center bg-primary text-white px-6 py-3 rounded-button font-semibold hover:bg-primary/90 transition-colors">
                            <i class="ri-external-link-line mr-2"></i>
                            View on Google Maps
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const contactForm = document.getElementById('contactForm');
            if (contactForm) {
                contactForm.addEventListener('submit', async function(e) {
                    e.preventDefault(); // Always prevent default
                    const submitBtn = this.querySelector('button[type="submit"]');
                    const originalText = submitBtn.innerHTML;
                    try {
                        // Get form data
                        const formData = new FormData(this);
                        const data = Object.fromEntries(formData);

                        // Basic validation
                        const requiredFields = ['firstName', 'lastName', 'email', 'phone', 'subject', 'message'];
                        const missingFields = requiredFields.filter(field => !data[field]);
                        if (missingFields.length > 0) {
                            throw new Error('Please fill in all required fields: ' + missingFields.join(', '));
                        }
                        if (!data.privacy) {
                            throw new Error('Please agree to the Privacy Policy and Terms of Service');
                        }
                        // Email validation
                        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                        if (!emailRegex.test(data.email)) {
                            throw new Error('Please enter a valid email address');
                        }

                        // Show loading
                        submitBtn.innerHTML = '<i class="ri-loader-4-line animate-spin mr-2"></i>Sending...';
                        submitBtn.disabled = true;

                        // Send to PHP handler
                        const response = await fetch('<?php echo getAssetUrl('process-form.php'); ?>?type=contact', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify(data)
                        });
                        if (!response.ok) {
                            throw new Error('Network response was not ok');
                        }
                        const result = await response.json();

                        // Create notification element
                        const notification = document.createElement('div');
                        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg ${result.success ? 'bg-green-500' : 'bg-red-500'} text-white max-w-md z-50 flex items-center`;
                        notification.innerHTML = `
                            <div class="flex-1">${result.message}</div>
                            <button class="ml-4 hover:text-gray-200" onclick="this.parentElement.remove()">
                                <i class="ri-close-line"></i>
                            </button>
                        `;
                        document.body.appendChild(notification);
                        setTimeout(() => { notification.remove(); }, 5000);
                        if (result.success) {
                            this.reset();
                        }
                    } catch (error) {
                        // Show error notification
                        const notification = document.createElement('div');
                        notification.className = 'fixed top-4 right-4 p-4 rounded-lg shadow-lg bg-red-500 text-white max-w-md z-50 flex items-center';
                        notification.innerHTML = `
                            <div class="flex-1">${error.message || 'There was an error submitting your message. Please try again or contact us directly.'}</div>
                            <button class="ml-4 hover:text-gray-200" onclick="this.parentElement.remove()">
                                <i class="ri-close-line"></i>
                            </button>
                        `;
                        document.body.appendChild(notification);
                        setTimeout(() => { notification.remove(); }, 5000);
                        console.error('Error:', error);
                    } finally {
                        submitBtn.innerHTML = originalText;
                        submitBtn.disabled = false;
                    }
                });
            }
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
