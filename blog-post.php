<?php 
require_once 'config.php';

// Sample blog post data (in a real application, this would come from a database)
$blog_posts = [
    1 => [
        'title' => 'The Future of Global Supply Chain: Trends to Watch in 2024',
        'content' => '
            <p>The global supply chain landscape is undergoing unprecedented transformation. As we navigate through 2024, several key trends are reshaping how businesses approach logistics, shipping, and international trade.</p>
            
            <h2>1. Artificial Intelligence and Machine Learning Integration</h2>
            <p>AI-powered logistics solutions are becoming mainstream, offering predictive analytics for demand forecasting, route optimisation, and inventory management. Companies implementing AI report up to 30% improvement in operational efficiency.</p>
            
            <h2>2. Sustainable Shipping Practices</h2>
            <p>Environmental consciousness is driving the adoption of green logistics solutions. From alternative fuels to carbon-neutral shipping options, sustainability is no longer optional but essential for competitive advantage.</p>
            
            <h2>3. Digital Documentation and Blockchain</h2>
            <p>The digitisation of trade documents and blockchain-based verification systems are reducing processing times and improving transparency across the supply chain.</p>
            
            <h2>4. Resilience and Risk Management</h2>
            <p>Recent global events have highlighted the importance of supply chain resilience. Companies are diversifying suppliers and implementing robust risk management strategies.</p>
            
            <h2>Conclusion</h2>
            <p>The future of global supply chain lies in embracing technology while maintaining focus on sustainability and resilience. Companies that adapt to these trends will be best positioned for success in the evolving logistics landscape.</p>
        ',
        'category' => 'industry-insights',
        'author' => 'PROMTECH Team',
        'date' => '2024-01-15',
        'image' => 'img8.webp',
        'tags' => ['Supply Chain', 'Technology', 'Trends'],
        'read_time' => '8 min read'
    ],
    2 => [
        'title' => 'Complete Guide to Ocean Freight: FCL vs LCL Shipping',
        'content' => '
            <p>Understanding the differences between Full Container Load (FCL) and Less than Container Load (LCL) shipping is crucial for optimising your logistics costs and delivery times.</p>
            
            <h2>What is FCL Shipping?</h2>
            <p>Full Container Load (FCL) means your cargo occupies an entire shipping container. This option is ideal for large shipments and offers several advantages:</p>
            <ul>
                <li>Faster transit times</li>
                <li>Lower risk of damage</li>
                <li>Better security</li>
                <li>Cost-effective for large volumes</li>
            </ul>
            
            <h2>What is LCL Shipping?</h2>
            <p>Less than Container Load (LCL) allows you to share container space with other shippers. This option is perfect for smaller shipments:</p>
            <ul>
                <li>Cost-effective for small volumes</li>
                <li>Flexible shipping schedules</li>
                <li>No minimum volume requirements</li>
                <li>Ideal for testing new markets</li>
            </ul>
            
            <h2>Making the Right Choice</h2>
            <p>The decision between FCL and LCL depends on several factors including shipment size, budget, timeline, and cargo type. Our experts can help you determine the best option for your specific needs.</p>
        ',
        'category' => 'shipping-guides',
        'author' => 'Maritime Expert',
        'date' => '2024-01-10',
        'image' => 'img9.webp',
        'tags' => ['Ocean Freight', 'FCL', 'LCL'],
        'read_time' => '12 min read'
    ]
];

// Get post ID from URL
$post_id = (int)($_GET['id'] ?? 1);
$post = $blog_posts[$post_id] ?? $blog_posts[1];

// Related posts (simplified - would be more sophisticated in real application)
$related_posts = array_filter($blog_posts, function($key) use ($post_id) {
    return $key !== $post_id;
}, ARRAY_FILTER_USE_KEY);
$related_posts = array_slice($related_posts, 0, 3, true);
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo htmlspecialchars($post['title']); ?> | <?php echo COMPANY_NAME; ?> Blog</title>
    <meta name="description" content="<?php echo htmlspecialchars(substr(strip_tags($post['content']), 0, 160)); ?>...">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colours: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .hero-banner {
            background: linear-gradient(rgba(30, 58, 138, 0.8), rgba(255, 122, 61, 0.6)), url('<?php echo getAssetUrl('assets/img/' . $post['image']); ?>');
            background-size: cover;
            background-position: centre centre;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        .blog-content h2 {
            @apply text-2xl font-bold text-secondary mt-8 mb-4;
        }
        .blog-content h3 {
            @apply text-xl font-semibold text-secondary mt-6 mb-3;
        }
        .blog-content p {
            @apply text-gray-700 leading-relaxed mb-6;
        }
        .blog-content ul, .blog-content ol {
            @apply text-gray-700 mb-6 pl-6;
        }
        .blog-content li {
            @apply mb-2;
        }
        .blog-content ul li {
            @apply list-disc;
        }
        .blog-content ol li {
            @apply list-decimal;
        }
    </style>
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>

    <!-- Hero Banner Section -->
    <section class="hero-banner min-h-[500px] flex items-center justify-center relative">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary/90 to-primary/70"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <nav class="flex items-center justify-center space-x-2 mb-6">
                    <a href="index.php" class="text-white/80 hover:text-white transition-colours">
                        <i class="ri-home-line mr-1"></i>Home
                    </a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <a href="blog.php" class="text-white/80 hover:text-white transition-colours">Blog</a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <span class="text-white font-medium">Article</span>
                </nav>
                <div class="max-w-4xl mx-auto">
                    <div class="flex items-center justify-center space-x-4 mb-6">
                        <span class="bg-white/20 backdrop-blur-sm text-white px-4 py-2 rounded-full text-sm font-medium">
                            <?php echo ucfirst(str_replace('-', ' ', $post['category'])); ?>
                        </span>
                        <span class="text-white/80"><?php echo $post['read_time']; ?></span>
                    </div>
                    <h1 class="text-4xl md:text-5xl font-bold mb-6"><?php echo htmlspecialchars($post['title']); ?></h1>
                    <div class="flex items-center justify-center space-x-6 text-white/90">
                        <div class="flex items-center">
                            <i class="ri-user-line mr-2"></i>
                            <span><?php echo htmlspecialchars($post['author']); ?></span>
                        </div>
                        <div class="flex items-center">
                            <i class="ri-calendar-line mr-2"></i>
                            <span><?php echo date('M j, Y', strtotime($post['date'])); ?></span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Article Content -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <div class="grid lg:grid-cols-4 gap-12">
                    <!-- Main Content -->
                    <div class="lg:col-span-3">
                        <article class="blog-content prose prose-lg max-w-none">
                            <?php echo $post['content']; ?>
                        </article>

                        <!-- Tags -->
                        <div class="mt-12 pt-8 border-t border-gray-200">
                            <h3 class="text-lg font-semibold text-secondary mb-4">Tags</h3>
                            <div class="flex flex-wrap gap-2">
                                <?php foreach ($post['tags'] as $tag): ?>
                                <span class="bg-primary/10 text-primary px-4 py-2 rounded-full text-sm font-medium">
                                    <?php echo htmlspecialchars($tag); ?>
                                </span>
                                <?php endforeach; ?>
                            </div>
                        </div>

                        <!-- Share Buttons -->
                        <div class="mt-8 pt-8 border-t border-gray-200">
                            <h3 class="text-lg font-semibold text-secondary mb-4">Share this article</h3>
                            <div class="flex space-x-4">
                                <a href="#" class="bg-blue-600 text-white p-3 rounded-full hover:bg-blue-700 transition-colours">
                                    <i class="ri-facebook-fill"></i>
                                </a>
                                <a href="#" class="bg-blue-400 text-white p-3 rounded-full hover:bg-blue-500 transition-colours">
                                    <i class="ri-twitter-fill"></i>
                                </a>
                                <a href="#" class="bg-blue-800 text-white p-3 rounded-full hover:bg-blue-900 transition-colours">
                                    <i class="ri-linkedin-fill"></i>
                                </a>
                                <a href="#" class="bg-gray-600 text-white p-3 rounded-full hover:bg-gray-700 transition-colours">
                                    <i class="ri-mail-fill"></i>
                                </a>
                            </div>
                        </div>
                    </div>

                    <!-- Sidebar -->
                    <div class="lg:col-span-1">
                        <!-- Author Info -->
                        <div class="bg-gray-50 p-6 rounded-2xl mb-8">
                            <div class="text-center">
                                <div class="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <i class="ri-user-line text-primary text-2xl"></i>
                                </div>
                                <h4 class="font-semibold text-secondary mb-2"><?php echo htmlspecialchars($post['author']); ?></h4>
                                <p class="text-gray-600 text-sm mb-4">
                                    Logistics expert with over 10 years of experience in international trade and supply chain management.
                                </p>
                                <div class="flex justify-center space-x-2">
                                    <a href="#" class="text-gray-400 hover:text-primary transition-colours">
                                        <i class="ri-linkedin-fill"></i>
                                    </a>
                                    <a href="#" class="text-gray-400 hover:text-primary transition-colours">
                                        <i class="ri-twitter-fill"></i>
                                    </a>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Contact -->
                        <div class="bg-gradient-to-br from-primary/10 to-secondary/10 p-6 rounded-2xl mb-8">
                            <h4 class="font-semibold text-secondary mb-4">Need Expert Advice?</h4>
                            <p class="text-gray-600 text-sm mb-4">
                                Get personalised logistics solutions from our expert team.
                            </p>
                            <a href="contact.php" class="bg-primary text-white px-6 py-3 rounded-full font-semibold hover:bg-primary/90 transition-colours flex items-center justify-center">
                                <i class="ri-message-line mr-2"></i>
                                Contact Us
                            </a>
                        </div>

                        <!-- Newsletter -->
                        <div class="bg-secondary text-white p-6 rounded-2xl">
                            <h4 class="font-semibold mb-4">Stay Updated</h4>
                            <p class="text-white/90 text-sm mb-4">
                                Subscribe to our newsletter for the latest logistics insights.
                            </p>
                            <form class="space-y-3">
                                <input type="email" placeholder="Your email" 
                                       class="w-full px-4 py-3 rounded-lg text-gray-900 focus:outline-none focus:ring-2 focus:ring-primary">
                                <button type="submit" 
                                        class="w-full bg-primary text-white py-3 rounded-lg font-semibold hover:bg-primary/90 transition-colours">
                                    Subscribe
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Related Articles -->
    <?php if (!empty($related_posts)): ?>
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <h2 class="text-3xl font-bold text-secondary mb-8">Related Articles</h2>
                <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <?php foreach ($related_posts as $id => $related_post): ?>
                    <article class="bg-white rounded-2xl shadow-lg overflow-hidden hover:shadow-xl transition-shadow">
                        <div class="relative">
                            <img src="<?php echo getAssetUrl('assets/img/' . $related_post['image']); ?>"
                                 alt="<?php echo htmlspecialchars($related_post['title']); ?>"
                                 class="w-full h-48 object-cover">
                            <div class="absolute top-4 left-4">
                                <span class="bg-primary text-white px-3 py-1 rounded-full text-sm font-medium">
                                    <?php echo ucfirst(str_replace('-', ' ', $related_post['category'])); ?>
                                </span>
                            </div>
                        </div>
                        <div class="p-6">
                            <div class="flex items-center text-sm text-gray-500 mb-3">
                                <span><?php echo $related_post['author']; ?></span>
                                <span class="mx-2">•</span>
                                <span><?php echo date('M j', strtotime($related_post['date'])); ?></span>
                            </div>
                            <h3 class="text-lg font-bold text-secondary mb-3 hover:text-primary transition-colours">
                                <a href="blog-post.php?id=<?php echo $id; ?>">
                                    <?php echo htmlspecialchars($related_post['title']); ?>
                                </a>
                            </h3>
                            <p class="text-gray-600 text-sm mb-4">
                                <?php echo htmlspecialchars(substr(strip_tags($related_post['content']), 0, 100)); ?>...
                            </p>
                            <a href="blog-post.php?id=<?php echo $id; ?>"
                               class="inline-flex items-center text-primary font-semibold hover:text-primary/80 transition-colours">
                                Read More
                                <i class="ri-arrow-right-line ml-2"></i>
                            </a>
                        </div>
                    </article>
                    <?php endforeach; ?>
                </div>

                <div class="text-center mt-12">
                    <a href="blog.php" class="bg-primary text-white px-8 py-4 rounded-full font-semibold hover:bg-primary/90 transition-colours inline-flex items-center">
                        <i class="ri-article-line mr-2"></i>
                        View All Articles
                    </a>
                </div>
            </div>
        </div>
    </section>
    <?php endif; ?>

    <script>
        // Smooth scroll for anchor links
        document.addEventListener('DOMContentLoaded', function() {
            // Add smooth scrolling to all links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Reading progress indicator
            const article = document.querySelector('.blog-content');
            if (article) {
                const progressBar = document.createElement('div');
                progressBar.className = 'fixed top-0 left-0 h-1 bg-primary z-50 transition-all duration-300';
                document.body.appendChild(progressBar);

                window.addEventListener('scroll', function() {
                    const articleTop = article.offsetTop;
                    const articleHeight = article.offsetHeight;
                    const windowHeight = window.innerHeight;
                    const scrollTop = window.pageYOffset;

                    const progress = Math.min(
                        Math.max((scrollTop - articleTop + windowHeight) / articleHeight, 0),
                        1
                    );

                    progressBar.style.width = (progress * 100) + '%';
                });
            }
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
