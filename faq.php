<?php 
require_once 'config.php';

// FAQ Categories and Questions
$faq_categories = [
    'shipping' => [
        'name' => 'Shipping & Logistics',
        'icon' => 'ri-ship-line',
        'questions' => [
            [
                'question' => 'What is the difference between FCL and LCL shipping?',
                'answer' => 'FCL (Full Container Load) means your cargo occupies an entire shipping container, while LCL (Less than Container Load) means you share container space with other shippers. FCL is typically more cost-effective for large shipments, while LCL is ideal for smaller volumes.'
            ],
            [
                'question' => 'How long does ocean freight shipping take?',
                'answer' => 'Ocean freight transit times vary by route. Typical times include: Asia to Europe (20-35 days), Asia to North America (15-25 days), Europe to North America (10-20 days). These times exclude customs clearance and inland transportation.'
            ],
            [
                'question' => 'What documents are required for international shipping?',
                'answer' => 'Essential documents include: Commercial Invoice, Packing List, Bill of Lading, Certificate of Origin, and any specific permits or certificates required by the destination country. Our team helps prepare all necessary documentation.'
            ],
            [
                'question' => 'Can you handle oversized or heavy cargo?',
                'answer' => 'Yes, we specialise in project cargo and heavy lift shipments. We have experience with machinery, industrial equipment, and oversized cargo requiring special handling, permits, and routing.'
            ],
            [
                'question' => 'What is cargo insurance and do I need it?',
                'answer' => 'Cargo insurance protects your goods against loss or damage during transit. While not mandatory, we highly recommend it for valuable shipments. We can arrange comprehensive coverage through our insurance partners.'
            ]
        ]
    ],
    'customs' => [
        'name' => 'Customs & Documentation',
        'icon' => 'ri-file-text-line',
        'questions' => [
            [
                'question' => 'What is customs clearance and how long does it take?',
                'answer' => 'Customs clearance is the process of getting approval from customs authorities to import or export goods. Typical clearance times range from 1-5 business days, depending on the country, cargo type, and documentation completeness.'
            ],
            [
                'question' => 'What are Incoterms and which should I choose?',
                'answer' => 'Incoterms define the responsibilities between buyer and seller in international trade. Common terms include FOB (Free on Board), CIF (Cost, Insurance, and Freight), and DDP (Delivered Duty Paid). We help you choose the most suitable terms for your shipment.'
            ],
            [
                'question' => 'How are customs duties and taxes calculated?',
                'answer' => 'Customs duties are calculated based on the HS (Harmonised System) code of your goods, their declared value, and the destination country\'s tariff rates. Additional taxes like VAT may also apply. We provide duty estimates before shipping.'
            ],
            [
                'question' => 'What happens if my shipment is held by customs?',
                'answer' => 'If customs holds your shipment, we immediately investigate the reason and work to resolve any issues. Common causes include missing documentation, valuation queries, or random inspections. We keep you informed throughout the process.'
            ],
            [
                'question' => 'Can you help with ATA Carnets for temporary exports?',
                'answer' => 'Yes, we assist with ATA Carnet applications for temporary exports such as trade show exhibits, professional equipment, and samples. This allows duty-free temporary importation in participating countries.'
            ]
        ]
    ],
    'pricing' => [
        'name' => 'Pricing & Payment',
        'icon' => 'ri-money-dollar-circle-line',
        'questions' => [
            [
                'question' => 'How do you calculate shipping costs?',
                'answer' => 'Shipping costs depend on factors including cargo weight/volume, origin/destination, service type, and current market rates. We provide detailed quotes including all charges such as terminal handling, documentation, and customs clearance.'
            ],
            [
                'question' => 'What payment methods do you accept?',
                'answer' => 'We accept bank transfers, letters of credit, and for established clients, credit terms. Payment terms are typically 30 days from invoice date for regular customers, with advance payment required for new clients.'
            ],
            [
                'question' => 'Are there any hidden fees in your quotes?',
                'answer' => 'No, we provide transparent pricing with all known charges included in our quotes. However, some charges like demurrage, detention, or additional customs inspections may occur and will be communicated immediately.'
            ],
            [
                'question' => 'Do you offer volume discounts?',
                'answer' => 'Yes, we offer competitive rates for regular shippers and volume commitments. Our account managers work with you to develop customised pricing based on your shipping patterns and volumes.'
            ],
            [
                'question' => 'What currency do you quote in?',
                'answer' => 'We typically quote in USD for international shipments, but can provide quotes in EUR, GBP, or other major currencies upon request. Exchange rate fluctuations may affect final charges.'
            ]
        ]
    ],
    'tracking' => [
        'name' => 'Tracking & Updates',
        'icon' => 'ri-map-pin-line',
        'questions' => [
            [
                'question' => 'How can I track my shipment?',
                'answer' => 'You can track your shipment using our online tracking system with your booking reference number. We also provide regular email updates at key milestones including departure, arrival, and customs clearance.'
            ],
            [
                'question' => 'What information is included in tracking updates?',
                'answer' => 'Our tracking updates include vessel/flight details, departure and arrival times, customs status, and any delays or issues. You\'ll receive notifications for all major milestones in your shipment\'s journey.'
            ],
            [
                'question' => 'Can I get SMS notifications for my shipment?',
                'answer' => 'Yes, we offer SMS notifications for critical updates such as departure, arrival, and delivery. This service can be activated when booking your shipment or by contacting our customer service team.'
            ],
            [
                'question' => 'What should I do if my shipment is delayed?',
                'answer' => 'If your shipment is delayed, we\'ll notify you immediately with the reason and revised timeline. Our team works proactively to minimise delays and find alternative solutions when necessary.'
            ],
            [
                'question' => 'How accurate are your delivery estimates?',
                'answer' => 'Our delivery estimates are based on historical data and current conditions. While we strive for accuracy, factors like weather, port congestion, and customs processing can affect actual delivery times.'
            ]
        ]
    ],
    'services' => [
        'name' => 'Services & Capabilities',
        'icon' => 'ri-service-line',
        'questions' => [
            [
                'question' => 'What types of cargo do you handle?',
                'answer' => 'We handle all types of cargo including general merchandise, machinery, agricultural products, textiles, electronics, and project cargo. We have expertise in both hazardous and non-hazardous materials.'
            ],
            [
                'question' => 'Do you provide door-to-door service?',
                'answer' => 'Yes, we offer comprehensive door-to-door services including pickup from your facility, export customs clearance, international transportation, import customs clearance, and delivery to the final destination.'
            ],
            [
                'question' => 'Can you handle time-sensitive shipments?',
                'answer' => 'Absolutely. We offer express services including air freight for urgent shipments and priority handling for time-critical cargo. Our team coordinates closely to meet your deadlines.'
            ],
            [
                'question' => 'Do you provide warehousing and storage services?',
                'answer' => 'Yes, we offer secure warehousing facilities at key locations for temporary storage, consolidation, and distribution services. Our warehouses are equipped with modern security and inventory management systems.'
            ],
            [
                'question' => 'What countries do you ship to and from?',
                'answer' => 'We provide services to and from over 100 countries worldwide. Our primary focus is on trade routes between Europe, Asia, Africa, and the Americas, with particular expertise in Ukrainian and regional markets.'
            ]
        ]
    ]
];

// Get current category filter
$current_category = $_GET['category'] ?? 'all';
$search_query = $_GET['search'] ?? '';
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Frequently Asked Questions | <?php echo COMPANY_NAME; ?></title>
    <meta name="description" content="Find answers to common questions about shipping, customs, pricing, tracking, and our logistics services. Get expert help from PROMTECH's FAQ section.">
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css" rel="stylesheet">
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colours: {
                        primary: '#FF7A3D',
                        secondary: '#1E3A8A',
                        accent: '#F59E0B'
                    }
                }
            }
        }
    </script>
    <style>
        .hero-banner {
            background: linear-gradient(rgba(30, 58, 138, 0.8), rgba(255, 122, 61, 0.6)), url('<?php echo getAssetUrl('assets/img/img14.webp'); ?>');
            background-size: cover;
            background-position: centre centre;
            background-repeat: no-repeat;
            background-attachment: fixed;
        }
        .faq-item {
            transition: all 0.3s ease;
        }
        .faq-item.active .faq-answer {
            max-height: 500px;
            opacity: 1;
        }
        .faq-answer {
            max-height: 0;
            opacity: 0;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        .category-card {
            transition: all 0.3s ease;
        }
        .category-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .category-filter {
            transition: all 0.3s ease;
        }
        .category-filter.active {
            background: linear-gradient(135deg, #FF7A3D, #1E3A8A);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-50">
    <?php include 'includes/header.php'; ?>

    <!-- Hero Banner Section -->
    <section class="hero-banner min-h-[500px] flex items-center justify-center relative">
        <div class="absolute inset-0 bg-gradient-to-r from-secondary/90 to-primary/70"></div>
        <div class="container mx-auto px-4 relative z-10">
            <div class="text-center text-white">
                <nav class="flex items-center justify-center space-x-2 mb-6">
                    <a href="index.php" class="text-white/80 hover:text-white transition-colours">
                        <i class="ri-home-line mr-1"></i>Home
                    </a>
                    <i class="ri-arrow-right-line text-white/60"></i>
                    <span class="text-white font-medium">FAQ</span>
                </nav>
                <h1 class="text-5xl md:text-6xl font-bold mb-6">Frequently Asked Questions</h1>
                <p class="text-xl text-white/90 max-w-3xl mx-auto mb-8">
                    Find quick answers to common questions about our logistics services, shipping processes, and more.
                </p>
                
                <!-- Search Bar -->
                <div class="max-w-2xl mx-auto">
                    <form method="GET" class="relative">
                        <input type="text" name="search" value="<?php echo htmlspecialchars($search_query); ?>" 
                               placeholder="Search FAQs..." 
                               class="w-full px-6 py-4 pl-12 rounded-full text-gray-900 focus:outline-none focus:ring-2 focus:ring-white">
                        <i class="ri-search-line absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 text-xl"></i>
                        <button type="submit" class="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primary text-white px-6 py-2 rounded-full font-semibold hover:bg-primary/90 transition-colours">
                            Search
                        </button>
                    </form>
                </div>
            </div>
        </div>
    </section>

    <!-- FAQ Categories -->
    <section class="py-20 bg-white">
        <div class="container mx-auto px-4">
            <div class="text-center mb-16">
                <span class="text-primary font-semibold text-lg">Quick Navigation</span>
                <h2 class="text-4xl font-bold text-secondary mt-2 mb-4">Browse by Category</h2>
                <p class="text-gray-600 max-w-2xl mx-auto">
                    Find answers organised by topic to quickly locate the information you need.
                </p>
            </div>

            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
                <?php foreach ($faq_categories as $key => $category): ?>
                <a href="#<?php echo $key; ?>" class="category-card bg-gradient-to-br from-gray-50 to-white p-8 rounded-2xl shadow-lg text-center">
                    <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                        <i class="<?php echo $category['icon']; ?> text-primary text-2xl"></i>
                    </div>
                    <h3 class="text-xl font-bold text-secondary mb-4"><?php echo $category['name']; ?></h3>
                    <p class="text-gray-600 mb-4">
                        <?php echo count($category['questions']); ?> questions answered
                    </p>
                    <span class="inline-flex items-center text-primary font-semibold">
                        View Questions
                        <i class="ri-arrow-right-line ml-2"></i>
                    </span>
                </a>
                <?php endforeach; ?>
            </div>

            <!-- Category Filter -->
            <div class="flex flex-wrap justify-center gap-4 mb-12">
                <button class="category-filter px-6 py-3 rounded-full font-medium transition-colours <?php echo $current_category === 'all' ? 'active' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>"
                        onclick="filterCategory('all')">
                    All Categories
                </button>
                <?php foreach ($faq_categories as $key => $category): ?>
                <button class="category-filter px-6 py-3 rounded-full font-medium transition-colours <?php echo $current_category === $key ? 'active' : 'bg-gray-200 text-gray-700 hover:bg-gray-300'; ?>"
                        onclick="filterCategory('<?php echo $key; ?>')">
                    <?php echo $category['name']; ?>
                </button>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section class="py-16 bg-gray-50">
        <div class="container mx-auto px-4">
            <div class="max-w-4xl mx-auto">
                <?php foreach ($faq_categories as $category_key => $category): ?>
                <div id="<?php echo $category_key; ?>" class="faq-category mb-16" data-category="<?php echo $category_key; ?>">
                    <div class="text-center mb-12">
                        <div class="bg-primary/10 w-16 h-16 rounded-full flex items-center justify-center mx-auto mb-6">
                            <i class="<?php echo $category['icon']; ?> text-primary text-2xl"></i>
                        </div>
                        <h2 class="text-3xl font-bold text-secondary mb-4"><?php echo $category['name']; ?></h2>
                        <p class="text-gray-600">
                            Common questions and detailed answers about <?php echo strtolower($category['name']); ?>.
                        </p>
                    </div>

                    <div class="space-y-6">
                        <?php foreach ($category['questions'] as $index => $faq): ?>
                        <div class="faq-item bg-white rounded-2xl shadow-lg overflow-hidden" data-search-content="<?php echo strtolower($faq['question'] . ' ' . $faq['answer']); ?>">
                            <button class="faq-question w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-primary/20"
                                    onclick="toggleFAQ(this)">
                                <div class="flex items-center justify-between">
                                    <h3 class="text-lg font-semibold text-secondary pr-4">
                                        <?php echo htmlspecialchars($faq['question']); ?>
                                    </h3>
                                    <div class="faq-icon text-primary text-xl transition-transform duration-300">
                                        <i class="ri-add-line"></i>
                                    </div>
                                </div>
                            </button>
                            <div class="faq-answer px-6 pb-6">
                                <div class="text-gray-600 leading-relaxed">
                                    <?php echo nl2br(htmlspecialchars($faq['answer'])); ?>
                                </div>
                            </div>
                        </div>
                        <?php endforeach; ?>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
        </div>
    </section>

    <!-- Contact CTA -->
    <section class="py-16 bg-gradient-to-r from-secondary to-primary">
        <div class="container mx-auto px-4">
            <div class="text-center text-white">
                <h2 class="text-3xl font-bold mb-4">Still Have Questions?</h2>
                <p class="text-xl text-white/90 max-w-2xl mx-auto mb-8">
                    Our expert team is here to help with any specific questions about your logistics needs.
                </p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="contact.php" class="bg-white text-secondary px-8 py-4 rounded-full font-semibold hover:bg-gray-100 transition-colours flex items-center justify-center">
                        <i class="ri-message-line mr-2"></i>
                        Contact Our Experts
                    </a>
                    <a href="tel:+1234567890" class="bg-white/10 backdrop-blur-sm text-white px-8 py-4 rounded-full font-semibold hover:bg-white/20 transition-colours flex items-center justify-center">
                        <i class="ri-phone-line mr-2"></i>
                        Call Us Now
                    </a>
                </div>
            </div>
        </div>
    </section>

    <script>
        // FAQ Functionality
        function toggleFAQ(button) {
            const faqItem = button.closest('.faq-item');
            const answer = faqItem.querySelector('.faq-answer');
            const icon = button.querySelector('.faq-icon i');

            // Close all other FAQ items
            document.querySelectorAll('.faq-item.active').forEach(item => {
                if (item !== faqItem) {
                    item.classList.remove('active');
                    const otherIcon = item.querySelector('.faq-icon i');
                    otherIcon.className = 'ri-add-line';
                }
            });

            // Toggle current FAQ item
            faqItem.classList.toggle('active');

            if (faqItem.classList.contains('active')) {
                icon.className = 'ri-subtract-line';
            } else {
                icon.className = 'ri-add-line';
            }
        }

        // Category Filtering
        function filterCategory(category) {
            // Update active filter button
            document.querySelectorAll('.category-filter').forEach(btn => {
                btn.classList.remove('active');
                btn.classList.add('bg-gray-200', 'text-gray-700', 'hover:bg-gray-300');
            });

            event.target.classList.add('active');
            event.target.classList.remove('bg-gray-200', 'text-gray-700', 'hover:bg-gray-300');

            // Show/hide categories
            document.querySelectorAll('.faq-category').forEach(categoryDiv => {
                if (category === 'all' || categoryDiv.dataset.category === category) {
                    categoryDiv.style.display = 'block';
                    // Animate in
                    categoryDiv.style.opacity = '0';
                    categoryDiv.style.transform = 'translateY(20px)';
                    setTimeout(() => {
                        categoryDiv.style.opacity = '1';
                        categoryDiv.style.transform = 'translateY(0)';
                        categoryDiv.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
                    }, 100);
                } else {
                    categoryDiv.style.display = 'none';
                }
            });

            // Update URL without page reload
            const url = new URL(window.location);
            if (category === 'all') {
                url.searchParams.delete('category');
            } else {
                url.searchParams.set('category', category);
            }
            window.history.pushState({}, '', url);
        }

        // Search Functionality
        function performSearch() {
            const searchTerm = document.querySelector('input[name="search"]').value.toLowerCase();
            let hasResults = false;

            document.querySelectorAll('.faq-item').forEach(item => {
                const searchContent = item.dataset.searchContent;
                const categoryDiv = item.closest('.faq-category');

                if (searchTerm === '' || searchContent.includes(searchTerm)) {
                    item.style.display = 'block';
                    categoryDiv.style.display = 'block';
                    hasResults = true;

                    // Highlight search terms
                    if (searchTerm !== '') {
                        const question = item.querySelector('.faq-question h3');
                        const answer = item.querySelector('.faq-answer div');

                        question.innerHTML = highlightText(question.textContent, searchTerm);
                        answer.innerHTML = highlightText(answer.textContent, searchTerm);
                    }
                } else {
                    item.style.display = 'none';
                }
            });

            // Show "no results" message if needed
            let noResultsMsg = document.querySelector('.no-results-message');
            if (!hasResults && searchTerm !== '') {
                if (!noResultsMsg) {
                    noResultsMsg = document.createElement('div');
                    noResultsMsg.className = 'no-results-message text-center py-12';
                    noResultsMsg.innerHTML = `
                        <div class="text-gray-400 text-6xl mb-4">
                            <i class="ri-search-line"></i>
                        </div>
                        <h3 class="text-xl font-semibold text-gray-600 mb-2">No results found</h3>
                        <p class="text-gray-500">Try different keywords or browse our categories above.</p>
                    `;
                    document.querySelector('.max-w-4xl').appendChild(noResultsMsg);
                }
                noResultsMsg.style.display = 'block';
            } else if (noResultsMsg) {
                noResultsMsg.style.display = 'none';
            }
        }

        function highlightText(text, searchTerm) {
            if (!searchTerm) return text;
            const regex = new RegExp(`(${searchTerm})`, 'gi');
            return text.replace(regex, '<mark class="bg-yellow-200 px-1 rounded">$1</mark>');
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            // Handle search on page load
            const urlParams = new URLSearchParams(window.location.search);
            const searchQuery = urlParams.get('search');
            if (searchQuery) {
                performSearch();
            }

            // Handle category filter on page load
            const categoryParam = urlParams.get('category');
            if (categoryParam && categoryParam !== 'all') {
                filterCategory(categoryParam);
            }

            // Add search event listeners
            const searchInput = document.querySelector('input[name="search"]');
            searchInput.addEventListener('input', performSearch);

            // Smooth scroll for category links
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // Animate FAQ items on scroll
            const observerOptions = {
                threshold: 0.1,
                rootMargin: '0px 0px -50px 0px'
            };

            const observer = new IntersectionObserver(function(entries) {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.style.opacity = '1';
                        entry.target.style.transform = 'translateY(0)';
                    }
                });
            }, observerOptions);

            document.querySelectorAll('.faq-item').forEach(item => {
                item.style.opacity = '0';
                item.style.transform = 'translateY(20px)';
                item.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
                observer.observe(item);
            });
        });
    </script>

    <?php include 'includes/footer.php'; ?>
</body>
</html>
